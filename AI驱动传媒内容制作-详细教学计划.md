# AI驱动的传媒内容制作 - 详细教学计划

## 课程基本信息
- **课程名称**：AI驱动的传媒内容制作
- **课程代码**：JOU2155A
- **学分**：2学分
- **课时**：32课时（16周，每周2课时）
- **开课单位**：长江新闻与传播学院

## 课程总体规划

### 教学阶段划分
1. **基础认知阶段**（第1-4周）：AI与LLM基础、提示词工程基础
2. **技能应用阶段**（第5-10周）：核心应用场景、创意生成
3. **进阶提升阶段**（第11-14周）：高级技巧、工具平台
4. **综合实践阶段**（第15-16周）：项目实战、成果展示

---

## 第1周：课程导论与AI基础

### 教学目标
- 理解AI发展的关键里程碑
- 掌握机器学习、深度学习的核心思想
- 了解LLM在传媒各环节的应用潜力
- 建立对AI技术的正确认知

### 教学重点
- AI发展简史中的关键节点
- 机器学习的基本概念
- LLM对传媒行业的影响

### 教学难点
- 理解AI技术的本质和局限性
- 建立理性的AI应用观念

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **课程导论**（15分钟）
   - 课程目标与学习成果
   - 考核方式说明
   - 学习方法指导

2. **AI发展简史**（20分钟）
   - 1956年达特茅斯会议
   - 专家系统时代
   - 机器学习兴起
   - 深度学习革命
   - 大模型时代

3. **互动讨论**（10分钟）
   - 学生分享对AI的认知
   - 讨论AI在日常生活中的应用

#### 第二节课（45分钟）
1. **机器学习核心思想**（20分钟）
   - 监督学习、无监督学习、强化学习
   - 深度学习与神经网络
   - 数据驱动的学习范式

2. **LLM概述**（20分钟）
   - 什么是大语言模型
   - LLM的突破性意义
   - 在传媒各环节的应用潜力

3. **课堂讨论**（5分钟）
   - AI是助手、伙伴还是颠覆者？

### PPT Slides大纲（25页）
1. 课程介绍（3页）
2. AI发展简史（8页）
3. 机器学习基础（6页）
4. LLM概述（5页）
5. 传媒应用展望（3页）

### 课前预习
**预习内容：**
- 观看视频：《人工智能简史》（30分钟）
- 阅读：AI在新闻业的应用案例（2-3个）

**预习题目：**
1. 你认为AI技术对传媒行业带来了哪些机遇和挑战？
2. 列举3个你知道的AI在传媒领域的应用实例
3. 你对"AI取代记者"这一观点有什么看法？

### 课后作业
1. **调研作业**：选择一个传媒机构，调研其AI技术应用现状，写一份500字的调研报告
2. **体验作业**：注册并体验至少2个AI工具（如ChatGPT、文心一言等），记录使用感受
3. **思考题**：结合专业背景，思考AI技术可能在哪些传媒工作环节发挥作用

### 推荐阅读材料
**必读：**
- 《人工智能简史》第1-2章
- 新华社《AI主播上岗记》案例分析

**推荐阅读：**
- 《智能时代》吴军著，第1-3章
- MIT Technology Review: "AI in Journalism"
- 路透社AI应用报告（2024）

**工具文档：**
- ChatGPT使用指南
- 文心一言功能介绍

---

## 第2周：LLM工作原理与技术基础

### 教学目标
- 了解Transformer架构的基本原理
- 理解Tokenization概念
- 掌握LLM的训练过程
- 认识LLM的能力边界与局限性

### 教学重点
- Transformer的注意力机制
- LLM的预训练和微调过程
- "幻觉"现象的成因

### 教学难点
- 注意力机制的直观理解
- 理解LLM为何会"一本正经胡说八道"

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **Transformer架构简介**（25分钟）
   - 注意力机制的直观理解
   - Self-Attention的工作原理
   - Encoder-Decoder结构

2. **Tokenization概念**（15分钟）
   - 什么是Token
   - 分词的不同方法
   - Token对模型理解的影响

3. **实践演示**（5分钟）
   - 在线Tokenizer工具演示

#### 第二节课（45分钟）
1. **LLM训练过程**（25分钟）
   - 预训练阶段：语言建模
   - 微调阶段：指令跟随
   - RLHF：人类反馈强化学习

2. **能力边界与局限性**（15分钟）
   - 为什么会产生"幻觉"
   - 知识截止时间问题
   - 逻辑推理的局限

3. **对比实验**（5分钟）
   - 不同LLM对同一问题的回答差异

### PPT Slides大纲（28页）
1. 回顾与导入（2页）
2. Transformer架构（10页）
3. Tokenization（4页）
4. LLM训练过程（8页）
5. 能力边界分析（4页）

### 课前预习
**预习内容：**
- 阅读：《注意力机制简介》
- 观看：Transformer原理动画视频

**预习题目：**
1. 什么是"注意力"？在人类认知中如何体现？
2. 你认为机器如何"理解"语言？
3. 为什么AI有时会给出错误但看似合理的答案？

### 课后作业
1. **实验作业**：使用3个不同的LLM（如ChatGPT、Claude、文心一言）回答同一个复杂问题，对比分析它们的回答差异
2. **概念梳理**：制作一个思维导图，梳理LLM的核心概念和工作流程
3. **案例分析**：收集3个AI"幻觉"的实例，分析可能的成因

### 推荐阅读材料
**必读：**
- 《Attention Is All You Need》论文简化版
- 《大语言模型原理解析》第2-3章

**推荐阅读：**
- 《深度学习》Goodfellow著，第12章
- OpenAI GPT系列论文概述
- 《AI幻觉现象研究报告》

**工具文档：**
- Hugging Face Tokenizer文档
- 各大模型技术报告对比

---

## 第3周：提示词工程基础

### 教学目标
- 理解提示词在LLM交互中的核心地位
- 掌握CRISPE框架构建优质提示词
- 熟练运用基础提示模式
- 避免常见的提示词错误

### 教学重点
- CRISPE框架的应用
- 四种基础提示模式
- 提示词优化技巧

### 教学难点
- 如何设计精确而不冗余的提示词
- 平衡指令的详细程度和灵活性

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **提示词的核心地位**（10分钟）
   - 提示词是人机交互的桥梁
   - 好提示词的重要性
   - 提示词工程的发展

2. **CRISPE框架详解**（25分钟）
   - **C**apacity and Role（能力与角色）
   - **I**nsight（洞察背景）
   - **S**tatement（任务陈述）
   - **P**ersonality（个性风格）
   - **E**xperiment（实验迭代）

3. **框架应用练习**（10分钟）
   - 学生练习用CRISPE框架设计提示词

#### 第二节课（45分钟）
1. **基础提示模式**（25分钟）
   - 指令型提示（Instruction）
   - 问答型提示（Q&A）
   - 补全型提示（Completion）
   - 对话型提示（Conversation）

2. **常见错误与改进**（15分钟）
   - 模糊不清的指令
   - 歧义表达
   - 隐含假设
   - 过度复杂

3. **提示词诊所**（5分钟）
   - 分析不良提示词案例
   - 集体改进练习

### PPT Slides大纲（30页）
1. 提示词重要性（3页）
2. CRISPE框架详解（12页）
3. 基础提示模式（8页）
4. 常见错误分析（5页）
5. 实践练习（2页）

### 课前预习
**预习内容：**
- 阅读：《提示词工程指南》第1章
- 体验：尝试与AI进行不同类型的对话

**预习题目：**
1. 你在使用AI工具时遇到过哪些沟通困难？
2. 如何让AI更好地理解你的需求？
3. 观察并记录3个你认为有效的提示词例子

### 课后作业
1. **设计练习**：针对以下场景设计提示词
   - 让AI帮你写一篇新闻稿
   - 让AI分析一篇文章的观点
   - 让AI生成社交媒体内容
2. **对比实验**：用不同的提示词完成同一任务，比较效果差异
3. **错误收集**：收集5个无效提示词，分析问题并改进

### 推荐阅读材料
**必读：**
- 《提示词工程完全指南》
- OpenAI官方提示词最佳实践

**推荐阅读：**
- 《与AI对话的艺术》
- Anthropic Claude提示词指南
- 《Prompt Engineering for Developers》

**工具文档：**
- ChatGPT提示词模板库
- 提示词测试平台使用指南

---

## 第4周：精确指令与格式控制

### 教学目标
- 掌握明确任务指令的技巧
- 学会提供充分的上下文信息
- 熟练控制AI输出的格式和风格
- 理解输出长度控制的方法

### 教学重点
- 任务指令的精确表达
- 上下文信息的有效组织
- 输出格式的多样化控制

### 教学难点
- 平衡指令详细度与执行效率
- 复杂格式要求的准确传达

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **明确任务指令**（20分钟）
   - 动词的选择：分析vs总结vs创作
   - 目标的明确：为谁做、做什么、达到什么效果
   - 约束条件的设定

2. **上下文信息提供**（20分钟）
   - 背景信息的重要性
   - 相关资料的整理方法
   - 示例的有效使用

3. **实践练习**（5分钟）
   - 学生练习改写模糊指令

#### 第二节课（45分钟）
1. **输出格式控制**（25分钟）
   - 列表格式（有序、无序）
   - 表格格式
   - Markdown格式
   - JSON格式基础
   - 特定模板格式

2. **风格与长度控制**（15分钟）
   - 正式vs非正式
   - 专业vs通俗
   - 简洁vs详细
   - 字数限制技巧

3. **综合练习**（5分钟）
   - 格式控制实战演练

### PPT Slides大纲（26页）
1. 任务指令设计（8页）
2. 上下文信息组织（6页）
3. 输出格式控制（8页）
4. 风格控制技巧（4页）

### 课前预习
**预习内容：**
- 学习：Markdown基础语法
- 了解：JSON数据格式

**预习题目：**
1. 什么情况下需要给AI提供背景信息？
2. 如何让AI的回答更符合特定受众的需求？
3. 尝试让AI用不同格式输出同一内容

### 课后作业
1. **格式练习**：让AI用以下格式输出内容
   - 新闻稿格式
   - 社交媒体帖子格式
   - 学术论文摘要格式
   - 产品说明书格式
2. **风格转换**：将同一段文字转换为不同风格
   - 学术风格→通俗风格
   - 正式风格→轻松风格
   - 客观风格→主观风格
3. **综合应用**：设计一个复杂的提示词，要求AI完成多层次的任务

### 推荐阅读材料
**必读：**
- 《高效提示词设计手册》
- Markdown语法完全指南

**推荐阅读：**
- 《技术写作风格指南》
- 《数据格式标准化指南》
- 各大媒体写作风格手册

**工具文档：**
- Markdown编辑器推荐
- JSON格式验证工具
- 文本格式转换工具

---

## 第5周：智能信息获取（基础）

### 教学目标
- 掌握利用LLM进行基础信息查询的方法
- 学会设计有效的查询型提示词
- 理解并应对AI"幻觉"现象
- 掌握交叉验证的基本方法

### 教学重点
- 查询型提示词的设计技巧
- "幻觉"现象的识别与应对
- 信息验证的重要性

### 教学难点
- 区分AI生成信息的可靠性
- 建立批判性思维习惯

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **LLM信息查询能力**（15分钟）
   - 事实查询vs概念解释
   - 背景信息搜集
   - 多角度信息获取

2. **查询型提示词设计**（20分钟）
   - 明确查询目标
   - 指定信息范围
   - 要求信息来源
   - 设置验证标准

3. **实践演示**（10分钟）
   - 现场查询演示
   - 学生互动练习

#### 第二节课（45分钟）
1. **"幻觉"现象深入分析**（20分钟）
   - 什么是AI幻觉
   - 幻觉产生的原因
   - 常见幻觉类型
   - 识别幻觉的方法

2. **交叉验证方法**（20分钟）
   - 多源验证
   - 权威来源对比
   - 逻辑一致性检查
   - 时效性验证

3. **实战练习**（5分钟）
   - 针对特定新闻事件进行信息搜集和验证

### PPT Slides大纲（28页）
1. 信息查询概述（4页）
2. 查询提示词设计（8页）
3. AI幻觉现象（8页）
4. 交叉验证方法（6页）
5. 实践案例（2页）

### 课前预习
**预习内容：**
- 阅读：《AI幻觉现象研究》
- 了解：事实核查的基本方法

**预习题目：**
1. 你如何判断网上信息的真实性？
2. AI可能在哪些方面提供错误信息？
3. 记录一次你发现AI回答错误的经历

### 课后作业
1. **信息验证练习**：选择一个近期新闻事件，使用AI搜集相关信息，并进行多源验证
2. **幻觉收集**：收集5个AI"幻觉"实例，分析其特点和可能原因
3. **查询优化**：设计10个不同类型的查询提示词，测试其效果

### 推荐阅读材料
**必读：**
- 《事实核查手册》
- 《AI可信度评估指南》

**推荐阅读：**
- 路透社事实核查方法论
- 《虚假信息识别技巧》
- 《数字时代的信息素养》

**工具文档：**
- 事实核查网站推荐
- 信息验证工具清单

---

## 第6周：智能信息获取（深度研究）

### 教学目标
- 掌握复杂问题的探究方法
- 学会文献综述辅助技巧
- 熟练进行多角度信息整合
- 提升研究型提示词设计能力

### 教学重点
- 研究型提示词的高级技巧
- 信息整合与分析方法
- 学术研究辅助应用

### 教学难点
- 复杂问题的分解与探究
- 保持研究的客观性和深度

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **复杂问题探究**（20分钟）
   - 问题分解技巧
   - 多层次信息需求
   - 渐进式深入方法

2. **文献综述辅助**（20分钟）
   - 主题文献搜集
   - 观点梳理整合
   - 研究脉络分析

3. **案例分析**（5分钟）
   - 学术研究实例展示

#### 第二节课（45分钟）
1. **多角度信息整合**（25分钟）
   - 不同立场观点收集
   - 信息权重评估
   - 综合分析框架

2. **研究型提示词进阶**（15分钟）
   - 限定研究范围
   - 追问细节技巧
   - 批判性分析要求

3. **实践项目**（5分钟）
   - 选择研究课题，开始初步文献回顾

### PPT Slides大纲（25页）
1. 深度研究概述（3页）
2. 复杂问题探究（8页）
3. 文献综述辅助（6页）
4. 信息整合方法（6页）
5. 实践指导（2页）

### 课前预习
**预习内容：**
- 阅读：《学术研究方法论》相关章节
- 了解：文献综述的基本结构

**预习题目：**
1. 如何系统性地研究一个复杂问题？
2. 文献综述的作用是什么？
3. 选择一个你感兴趣的传媒话题，思考研究角度

### 课后作业
1. **研究项目**：选择一个传媒相关研究课题，使用AI辅助完成：
   - 背景资料收集
   - 相关文献梳理
   - 多角度观点整理
2. **方法论总结**：总结使用AI进行深度研究的方法和技巧
3. **批判性分析**：评估AI在学术研究中的优势和局限性

### 推荐阅读材料
**必读：**
- 《学术研究与AI辅助》
- 《信息整合分析方法》

**推荐阅读：**
- 《传媒研究方法论》
- 《数字人文研究指南》
- 《批判性思维训练》

**工具文档：**
- 学术搜索引擎使用指南
- 文献管理工具推荐

---

## 第7周：文本摘要与提炼（基础）

### 教学目标
- 理解摘要类型和生成原理
- 掌握基础摘要提示词设计
- 熟练应用于不同场景
- 提升信息提炼能力

### 教学重点
- 抽取式vs生成式摘要的区别
- 摘要长度和要点控制
- 不同文本类型的摘要策略

### 教学难点
- 保持摘要的准确性和完整性
- 平衡简洁性与信息量

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **摘要类型与原理**（20分钟）
   - 抽取式摘要：关键句提取
   - 生成式摘要：重新表述
   - LLM生成摘要的机制
   - 摘要质量评估标准

2. **基础摘要提示词**（20分钟）
   - 长度控制技巧
   - 要点数量指定
   - 受众导向设计
   - 格式要求设定

3. **实践演示**（5分钟）
   - 现场摘要生成演示

#### 第二节课（45分钟）
1. **应用场景分析**（25分钟）
   - 新闻快讯摘要
   - 会议纪要整理
   - 报告阅读摘要
   - 社交媒体内容摘要

2. **不同文本类型处理**（15分钟）
   - 新闻报道摘要
   - 评论文章摘要
   - 访谈内容摘要
   - 学术论文摘要

3. **摘要练习**（5分钟）
   - 学生实践不同类型文本摘要

### PPT Slides大纲（24页）
1. 摘要概述（4页）
2. 摘要类型与原理（6页）
3. 提示词设计（6页）
4. 应用场景（6页）
5. 实践指导（2页）

### 课前预习
**预习内容：**
- 阅读：《文本摘要技术概述》
- 收集：不同类型的文本材料

**预习题目：**
1. 好的摘要应该具备哪些特点？
2. 不同类型的文本需要不同的摘要策略吗？
3. 尝试手动为一篇文章写摘要，记录遇到的困难

### 课后作业
1. **摘要练习**：为以下类型的文本生成摘要
   - 一篇新闻报道（200字摘要）
   - 一次会议记录（要点式摘要）
   - 一篇学术论文（结构化摘要）
2. **对比分析**：比较AI摘要与人工摘要的差异
3. **应用设计**：设计3个不同场景的摘要应用方案

### 推荐阅读材料
**必读：**
- 《自动文摘技术发展》
- 《新闻摘要写作规范》

**推荐阅读：**
- 《信息压缩与提炼技巧》
- 《会议纪要标准格式》
- 《学术摘要写作指南》

**工具文档：**
- 摘要质量评估标准
- 不同媒体摘要风格指南

---

## 第8周：文本摘要与提炼（高级）

### 教学目标
- 掌握特定受众摘要生成技巧
- 学会关键信息提取方法
- 熟练进行结构化信息提取
- 提升数据处理能力

### 教学重点
- 受众导向的摘要定制
- 实体、观点、数据提取
- 非结构化到结构化转换

### 教学难点
- 复杂信息的结构化处理
- 保持信息提取的准确性

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **特定受众摘要**（25分钟）
   - 专业vs通俗受众
   - 不同年龄层适配
   - 文化背景考虑
   - 使用场景定制

2. **风格化摘要生成**（15分钟）
   - 正式vs非正式风格
   - 客观vs主观视角
   - 详细vs简洁程度

3. **实践练习**（5分钟）
   - 同一内容的多版本摘要

#### 第二节课（45分钟）
1. **关键信息提取**（25分钟）
   - 实体识别与提取
   - 观点态度分析
   - 关键数据提取
   - 情感倾向判断

2. **结构化信息提取**（15分钟）
   - 表格数据生成
   - 分类信息整理
   - 时间线构建
   - 关系图谱提取

3. **综合应用**（5分钟）
   - 用户评论分析实践

### PPT Slides大纲（26页）
1. 高级摘要概述（3页）
2. 受众导向摘要（8页）
3. 关键信息提取（8页）
4. 结构化处理（5页）
5. 综合应用（2页）

### 课前预习
**预习内容：**
- 了解：数据结构化的基本概念
- 学习：情感分析基础知识

**预习题目：**
1. 如何为不同受众定制信息？
2. 什么是结构化数据？有什么优势？
3. 收集一些用户评论，尝试分析其情感倾向

### 课后作业
1. **定制摘要**：为同一篇文章生成面向不同受众的摘要版本
2. **信息提取项目**：
   - 从产品评论中提取优缺点
   - 从新闻报道中提取关键事实
   - 将访谈内容转换为结构化数据
3. **工具开发**：设计一个信息提取的标准流程

### 推荐阅读材料
**必读：**
- 《结构化数据处理指南》
- 《情感分析技术应用》

**推荐阅读：**
- 《用户研究方法论》
- 《数据新闻制作指南》
- 《信息可视化基础》

**工具文档：**
- 数据结构化工具推荐
- 情感分析API文档

---

## 第9周：创意生成（选题策划）

### 教学目标
- 掌握AI辅助创意拓展技巧
- 学会选题策划的系统方法
- 熟练进行受众兴趣分析
- 提升创意思维能力

### 教学重点
- 创意生成的多角度思考
- 选题策划的实用方法
- 热点追踪与趋势分析

### 教学难点
- 平衡创意性与可行性
- 确保选题的新闻价值

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **AI创意拓展技巧**（25分钟）
   - 头脑风暴增强
   - 多角度思考法
   - 反向思考技巧
   - 联想扩展方法

2. **创意评估标准**（15分钟）
   - 新颖性评估
   - 可行性分析
   - 受众吸引力
   - 传播价值判断

3. **实践演示**（5分钟）
   - 现场创意生成演示

#### 第二节课（45分钟）
1. **选题策划方法**（25分钟）
   - 热点追踪技巧
   - 受众兴趣分析
   - 内容日历规划
   - 选题角度挖掘

2. **案例分析与实践**（15分钟）
   - 成功选题案例分析
   - 学生选题练习

3. **创意工作坊**（5分钟）
   - 针对"大学生就业"主题生成10个报道角度

### PPT Slides大纲（28页）
1. 创意生成概述（4页）
2. AI创意拓展技巧（10页）
3. 选题策划方法（8页）
4. 案例分析（4页）
5. 实践指导（2页）

### 课前预习
**预习内容：**
- 阅读：《创意思维训练》相关章节
- 观察：近期热门话题和讨论

**预习题目：**
1. 你通常如何产生新的想法？
2. 什么样的新闻选题容易引起关注？
3. 列举5个你认为有潜力的新闻选题

### 课后作业
1. **创意练习**：
   - 为"环保"主题生成20个不同角度的内容创意
   - 设计一个月的内容日历
   - 分析3个热门话题的传播特点
2. **选题方案**：选择一个社会热点，设计完整的报道策划方案
3. **创意工具箱**：整理个人的创意生成方法和技巧

### 推荐阅读材料
**必读：**
- 《新闻选题策划指南》
- 《创意思维与传媒实践》

**推荐阅读：**
- 《热点追踪与趋势分析》
- 《受众心理学》
- 《内容营销策略》

**工具文档：**
- 热点监测工具推荐
- 内容日历模板

---

## 第10周：创意生成（辅助写作）

### 教学目标
- 掌握AI辅助内容框架搭建
- 学会多样化表达生成技巧
- 熟练进行文本润色改写
- 提升写作效率和质量

### 教学重点
- 内容框架的系统构建
- 文本润色的多种技巧
- 风格模仿与转换

### 教学难点
- 保持内容的原创性和个性
- 平衡AI辅助与人工创作

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **内容框架搭建**（25分钟）
   - 大纲生成技巧
   - 脚本结构设计
   - 逻辑链条构建
   - 段落组织方法

2. **初稿生成辅助**（15分钟）
   - 开头段落生成
   - 过渡句设计
   - 结尾总结技巧

3. **实践演示**（5分钟）
   - 现场框架搭建演示

#### 第二节课（45分钟）
1. **文本润色技巧**（25分钟）
   - 扩写与缩写
   - 语言简化
   - 专业度提升
   - 可读性优化

2. **风格转换实践**（15分钟）
   - 正式↔非正式
   - 客观↔主观
   - 学术↔通俗
   - 严肃↔轻松

3. **综合练习**（5分钟）
   - 平实报道改写为吸引人的社交媒体文案

### PPT Slides大纲（25页）
1. 辅助写作概述（3页）
2. 框架搭建技巧（8页）
3. 文本润色方法（8页）
4. 风格转换实践（4页）
5. 综合应用（2页）

### 课前预习
**预习内容：**
- 阅读：《写作技巧提升指南》
- 分析：不同媒体的写作风格

**预习题目：**
1. 你在写作时通常遇到哪些困难？
2. 如何让文章更有吸引力？
3. 收集3种不同风格的文章，分析其特点

### 课后作业
1. **写作实践**：
   - 使用AI辅助完成一篇800字的新闻报道
   - 将学术论文摘要改写为通俗易懂的科普文章
   - 模仿某位知名作家的风格写一段文字
2. **润色练习**：对自己以前的文章进行AI辅助润色
3. **风格库建设**：收集并分析10种不同的写作风格

### 推荐阅读材料
**必读：**
- 《AI时代的写作技巧》
- 《多媒体写作指南》

**推荐阅读：**
- 《风格的要素》
- 《新闻写作教程》
- 《创意写作技巧》

**工具文档：**
- 写作辅助工具推荐
- 文本分析工具使用指南

---

## 第11周：高级提示技巧（Few-Shot与角色扮演）

### 教学目标
- 深入理解Zero-Shot vs Few-Shot的区别
- 掌握Few-Shot示例设计技巧
- 熟练运用角色扮演技术
- 提升复杂任务处理能力

### 教学重点
- Few-Shot示例的有效设计
- 角色扮演的最佳实践
- 复杂任务的分解与处理

### 教学难点
- 示例质量对结果的影响
- 角色一致性的维持

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **Few-Shot技术深入**（25分钟）
   - Zero-Shot vs Few-Shot对比
   - 示例选择原则
   - 示例数量优化
   - 示例质量控制

2. **Few-Shot设计实践**（15分钟）
   - 任务类型分析
   - 示例模板设计
   - 效果测试方法

3. **实践演示**（5分钟）
   - Few-Shot评论打分模式演示

#### 第二节课（45分钟）
1. **角色扮演技术**（25分钟）
   - 角色设定要素
   - 背景信息构建
   - 行为模式定义
   - 一致性维持

2. **高级应用场景**（15分钟）
   - 专家咨询模拟
   - 用户访谈模拟
   - 多角色对话
   - 反对观点生成

3. **综合练习**（5分钟）
   - 多角色模拟对话实践

### PPT Slides大纲（27页）
1. 高级技巧概述（3页）
2. Few-Shot技术详解（10页）
3. 角色扮演技术（10页）
4. 应用场景分析（3页）
5. 实践指导（1页）

### 课前预习
**预习内容：**
- 复习：基础提示词技巧
- 思考：角色扮演在传媒中的应用

**预习题目：**
1. 什么情况下需要给AI提供示例？
2. 如何让AI更好地扮演特定角色？
3. 设计一个需要专业知识的咨询场景

### 课后作业
1. **Few-Shot练习**：
   - 设计一个新闻标题评分系统
   - 创建文章分类模型
   - 建立情感分析标准
2. **角色扮演项目**：
   - 让AI扮演资深记者进行新闻分析
   - 模拟专家访谈对话
   - 创建多角色辩论场景
3. **技巧总结**：整理个人的高级提示技巧库

### 推荐阅读材料
**必读：**
- 《高级提示工程技术》
- 《AI角色扮演应用指南》

**推荐阅读：**
- 《机器学习中的Few-Shot学习》
- 《对话系统设计原理》
- 《人机交互心理学》

**工具文档：**
- 提示词模板库
- 角色设定参考手册

---

## 第12周：思维链（Chain of Thought）技术

### 教学目标
- 深入理解CoT的工作原理
- 掌握不同CoT提示方法
- 熟练应用于复杂推理任务
- 提升逻辑分析能力

### 教学重点
- CoT技术的核心机制
- 多步骤推理的设计
- Self-Consistency方法

### 教学难点
- 复杂推理链的构建
- 推理质量的评估

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **CoT原理深入探讨**（20分钟）
   - 思维链的概念
   - 逐步推理的优势
   - 认知科学基础
   - 适用场景分析

2. **CoT提示方法**（20分钟）
   - Zero-shot CoT ("让我们一步步思考")
   - Manual CoT (手动示例)
   - Auto CoT (自动生成)
   - 方法对比分析

3. **基础练习**（5分钟）
   - 简单逻辑推理演示

#### 第二节课（45分钟）
1. **高级CoT技术**（25分钟）
   - Self-Consistency技术
   - 多路径推理
   - 结果整合方法
   - 质量评估标准

2. **传媒应用实践**（15分钟）
   - 公关策略风险评估
   - 新闻事件因果分析
   - 舆情发展预测
   - 媒体效果评估

3. **复杂案例练习**（5分钟）
   - 多步骤传媒分析问题解决

### PPT Slides大纲（26页）
1. CoT技术概述（4页）
2. CoT原理与方法（10页）
3. 高级CoT技术（6页）
4. 传媒应用案例（4页）
5. 实践指导（2页）

### 课前预习
**预习内容：**
- 阅读：《逻辑推理基础》
- 思考：复杂问题的分解方法

**预习题目：**
1. 你如何解决复杂的问题？
2. 逐步推理有什么优势？
3. 设计一个需要多步推理的传媒问题

### 课后作业
1. **CoT应用练习**：
   - 使用CoT分析一个公关危机案例
   - 评估某媒体策略的潜在风险
   - 分析新闻事件的深层原因
2. **方法对比**：比较不同CoT方法在同一问题上的表现
3. **推理链设计**：为复杂传媒问题设计标准推理流程

### 推荐阅读材料
**必读：**
- 《思维链推理技术详解》
- 《逻辑分析在传媒中的应用》

**推荐阅读：**
- 《批判性思维训练》
- 《决策分析方法论》
- 《风险评估技术》

**工具文档：**
- CoT提示词模板
- 推理质量评估标准

---

## 第13周：主流LLM平台对比体验

### 教学目标
- 深入了解各大LLM平台特点
- 掌握平台选择的评估标准
- 熟练进行横向对比测评
- 提升工具选择能力

### 教学重点
- 主流平台的功能特点
- 性能对比方法
- 使用场景匹配

### 教学难点
- 客观评估不同平台的优劣
- 根据需求选择合适工具

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **国外主流平台**（25分钟）
   - ChatGPT/GPT-4特点分析
   - Claude功能优势
   - Gemini技术特色
   - 使用限制与成本

2. **国内主流平台**（15分钟）
   - 文心一言
   - 通义千问
   - 讯飞星火
   - 智谱清言

3. **注册与基础操作**（5分钟）
   - 账号注册指导
   - 基本界面介绍

#### 第二节课（45分钟）
1. **新兴平台介绍**（20分钟）
   - Kimi Chat长文本优势
   - 腾讯元宝
   - 豆包
   - 其他特色平台

2. **横向测评实践**（20分钟）
   - 统一测试任务设计
   - 评估维度确定
   - 结果记录与分析

3. **平台选择策略**（5分钟）
   - 需求匹配原则
   - 成本效益分析

### PPT Slides大纲（30页）
1. 平台概览（4页）
2. 国外平台详解（10页）
3. 国内平台分析（8页）
4. 对比测评方法（6页）
5. 选择策略（2页）

### 课前预习
**预习内容：**
- 注册：至少3个不同的LLM平台账号
- 了解：各平台的基本功能

**预习题目：**
1. 你已经使用过哪些AI平台？
2. 不同平台给你的感受有什么差异？
3. 你认为评估AI平台应该看哪些方面？

### 课后作业
1. **全面测评项目**：
   - 设计10个测试任务
   - 在5个不同平台上执行
   - 制作详细对比报告
2. **专项测试**：
   - 长文本处理能力测试
   - 专业知识准确性测试
   - 创意生成能力测试
3. **使用指南**：为特定传媒任务推荐最适合的平台

### 推荐阅读材料
**必读：**
- 《LLM平台评测报告2024》
- 各平台官方技术文档

**推荐阅读：**
- 《AI工具选择指南》
- 《企业AI应用策略》
- 最新的平台评测文章

**工具文档：**
- 平台功能对比表
- 测评标准模板

---

## 第14周：AI辅助工具与集成应用

### 教学目标
- 了解集成LLM的专业软件
- 掌握特定领域AI工具使用
- 理解API概念和应用
- 提升工作流程优化能力

### 教学重点
- 专业AI工具的特色功能
- 工作流程集成方法
- 效率提升策略

### 教学难点
- 选择合适的工具组合
- 建立高效的工作流程

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **集成LLM软件介绍**（25分钟）
   - 豆包：办公场景集成
   - 腾讯元宝：多模态功能
   - ima：创意设计辅助
   - 其他集成应用

2. **特定领域工具**（15分钟）
   - Trae：写作辅助
   - Cursor：代码辅助
   - 设计类AI工具
   - 数据分析工具

3. **工具演示**（5分钟）
   - 现场操作演示

#### 第二节课（45分钟）
1. **API概念简介**（20分钟）
   - 什么是API
   - API的应用场景
   - 成本与限制
   - 安全考虑

2. **工作流程优化**（20分钟）
   - 资料整理自动化
   - 写作辅助流程
   - 内容审核辅助
   - 数据分析集成

3. **工具体验实践**（5分钟）
   - 学生体验不同工具功能

### PPT Slides大纲（24页）
1. AI工具生态（3页）
2. 集成软件介绍（8页）
3. 专业工具分析（6页）
4. API概念（4页）
5. 工作流程优化（3页）

### 课前预习
**预习内容：**
- 下载：推荐的AI辅助软件
- 了解：API的基本概念

**预习题目：**
1. 你希望AI在哪些具体工作环节帮助你？
2. 什么样的工具集成最有价值？
3. 尝试使用一个新的AI工具，记录体验

### 课后作业
1. **工具测评**：
   - 深度体验3个不同类型的AI工具
   - 分析其在传媒工作中的应用价值
   - 制作工具使用指南
2. **工作流程设计**：设计一个AI辅助的内容生产流程
3. **效率分析**：对比使用AI工具前后的工作效率变化

### 推荐阅读材料
**必读：**
- 《AI工具集成应用指南》
- 《工作流程自动化》

**推荐阅读：**
- 《数字化办公解决方案》
- 《API应用开发基础》
- 《效率提升方法论》

**工具文档：**
- 推荐AI工具清单
- 工作流程模板

---

## 第15周：期末项目准备与指导

### 教学目标
- 明确期末项目要求和评估标准
- 掌握项目规划和执行方法
- 综合运用所学LLM技能
- 提升项目管理能力

### 教学重点
- 项目选题与规划
- 技能综合应用
- 项目执行管理

### 教学难点
- 项目范围的合理控制
- 技能的有机整合

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **期末项目介绍**（20分钟）
   - 项目类型选择：
     * 播客节目策划制作
     * 社交媒体账号运营
     * 深度报道完成
     * 数据新闻制作
     * 其他创新项目

2. **项目要求详解**（20分钟）
   - 技能应用要求
   - 成果展示标准
   - 评估维度说明
   - 时间安排规划

3. **选题指导**（5分钟）
   - 项目可行性评估
   - 个人兴趣匹配

#### 第二节课（45分钟）
1. **项目规划方法**（25分钟）
   - 项目分解技巧
   - 时间管理方法
   - 资源配置策略
   - 风险预案设计

2. **技能整合指导**（15分钟）
   - 信息搜集→内容生成→摘要提炼
   - 提示词技巧的综合运用
   - 质量控制方法

3. **项目启动**（5分钟）
   - 确定项目方向
   - 制定执行计划

### PPT Slides大纲（22页）
1. 项目概述（4页）
2. 项目类型介绍（8页）
3. 要求与标准（4页）
4. 规划方法（4页）
5. 执行指导（2页）

### 课前预习
**预习内容：**
- 回顾：本学期所学的所有技能
- 思考：个人感兴趣的项目方向

**预习题目：**
1. 你最想完成什么类型的传媒项目？
2. 如何展示你掌握的AI技能？
3. 预估项目执行中可能遇到的困难

### 课后作业
1. **项目提案**：
   - 确定项目类型和主题
   - 制定详细执行计划
   - 列出需要运用的AI技能
   - 设计成果展示方案
2. **技能清单**：梳理个人掌握的AI技能
3. **开始执行**：启动项目第一阶段工作

### 推荐阅读材料
**必读：**
- 《项目管理基础》
- 《传媒项目案例集》

**推荐阅读：**
- 《创新项目设计》
- 《成果展示技巧》
- 优秀学生项目案例

**工具文档：**
- 项目规划模板
- 技能应用检查清单

---

## 第16周：项目展示与课程总结

### 教学目标
- 展示期末项目成果
- 进行同伴互评和反思
- 总结课程知识体系
- 规划未来学习路径

### 教学重点
- 项目成果展示
- 学习成果总结
- 未来发展规划

### 教学难点
- 客观评估学习成果
- 制定可行的发展计划

### 课堂教学安排（90分钟）

#### 第一节课（45分钟）
1. **项目成果展示**（35分钟）
   - 学生项目展示（每人3-5分钟）
   - 成果亮点分析
   - 技能应用评估

2. **同伴互评**（10分钟）
   - 项目评价标准
   - 互评反馈收集

#### 第二节课（45分钟）
1. **课程知识体系回顾**（20分钟）
   - AI与LLM基础
   - 提示词工程技巧
   - 核心应用场景
   - 高级技术方法
   - 工具平台应用

2. **学习成果总结**（15分钟）
   - 个人技能提升盘点
   - 学习心得分享
   - 困难与突破回顾

3. **未来发展规划**（10分钟）
   - LLM技术发展趋势
   - 个人学习路径建议
   - 持续学习资源推荐

### PPT Slides大纲（20页）
1. 项目展示安排（2页）
2. 评价标准（3页）
3. 知识体系回顾（8页）
4. 发展趋势（4页）
5. 学习建议（3页）

### 课前预习
**预习内容：**
- 完善：期末项目成果
- 准备：项目展示材料

**预习题目：**
1. 你的项目最大的亮点是什么？
2. 在项目中你学到了什么？
3. 你计划如何继续学习AI技术？

### 课后作业
1. **项目总结报告**：
   - 项目执行过程记录
   - 技能应用分析
   - 学习心得总结
   - 改进建议
2. **学习规划**：制定个人的AI学习发展计划
3. **课程反馈**：对课程内容和教学方式提出建议

### 推荐阅读材料
**必读：**
- 《AI技术发展趋势报告》
- 《终身学习指南》

**推荐阅读：**
- 《传媒行业AI应用前景》
- 《个人技能发展规划》
- 最新的AI技术资讯

**工具文档：**
- 持续学习资源清单
- 技能发展路径图

---

## 课程总结与评估

### 课程目标达成情况

#### 知识目标
- ✅ 理解AI发展历程和LLM基本原理
- ✅ 掌握提示词工程的核心技巧
- ✅ 熟悉主流LLM平台和工具

#### 技能目标
- ✅ 能够熟练使用LLM进行信息获取和处理
- ✅ 掌握AI辅助的内容创作和编辑技巧
- ✅ 具备复杂任务的AI解决方案设计能力

#### 应用目标
- ✅ 能够将AI技术应用于传媒工作流程
- ✅ 具备AI工具选择和使用的判断能力
- ✅ 培养了AI时代的职业素养和伦理意识

### 教学方法总结

#### ASK教学法应用
- **A (Attitude)**：通过中华优秀传统文化传播案例培养使命感
- **S (Skill)**：系统训练LLM应用技能
- **K (Knowledge)**：深入理解AI技术原理

#### 实践导向
- 每周都包含实际操作练习
- 项目驱动的综合应用
- 真实场景的案例分析

#### 渐进式学习
- 从基础概念到高级技巧
- 从单一技能到综合应用
- 从模仿学习到创新实践

### 持续改进建议

#### 内容更新
- 定期更新最新的AI技术发展
- 增加更多行业应用案例
- 关注AI伦理和社会责任议题

#### 教学方法
- 增加更多互动环节
- 引入同伴学习机制
- 加强个性化指导

#### 评估方式
- 完善项目评估标准
- 增加过程性评估
- 建立长期跟踪机制

---

## 附录

### A. 推荐学习资源

#### 在线课程
- Coursera: "AI for Everyone"
- edX: "Introduction to Artificial Intelligence"
- 中国大学MOOC: "人工智能与创新"

#### 专业书籍
- 《人工智能：一种现代的方法》
- 《深度学习》
- 《自然语言处理综论》

#### 技术博客
- OpenAI Blog
- Anthropic Research
- Google AI Blog
- 百度AI技术博客

#### 工具平台
- Hugging Face
- GitHub Copilot
- Notion AI
- 各大LLM平台

### B. 技能评估清单

#### 基础技能
- [ ] 理解AI基本概念
- [ ] 掌握LLM工作原理
- [ ] 熟练使用基础提示词

#### 进阶技能
- [ ] 设计复杂提示词
- [ ] 运用高级技巧
- [ ] 选择合适工具

#### 应用技能
- [ ] 信息获取与验证
- [ ] 内容创作与编辑
- [ ] 项目综合应用

### C. 常见问题解答

#### Q1: 如何避免过度依赖AI？
保持批判性思维，将AI作为辅助工具而非替代品。

#### Q2: 如何确保AI生成内容的质量？
建立验证机制，多源对比，人工审核。

#### Q3: 如何跟上AI技术的快速发展？
建立持续学习习惯，关注技术动态，实践新工具。

---

*本教学计划将根据技术发展和教学实践持续更新优化*