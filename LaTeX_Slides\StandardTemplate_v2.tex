% AI驱动的传媒内容制作课程 - 标准LaTeX模板
% 基于Week8和Week9的最佳实践设计
% 版本: v2.0
% 创建时间: 2025-08-06

\documentclass[aspectratio=169,xcolor=dvipsnames]{beamer}

% 核心包声明 - 按功能分组
% 中文支持
\usepackage[UTF8]{ctex}

% 图形和可视化
\usepackage{tikz}
\usepackage{pgfplots}
\usepackage{xcolor}
\usepackage{graphicx}

% 数学和算法
\usepackage{amsmath}
\usepackage{algorithm}
\usepackage{algorithmic}

% 表格和列表
\usepackage{booktabs}
\usepackage{array}
\usepackage{enumitem}

% 图标和装饰
\usepackage{fontawesome5}
\usepackage{tcolorbox}

% 高级功能
\usepackage{forest}
\usepackage{hyperref}

% 主题设置
\usetheme{Madrid}
% 注意: 每周使用不同主题色，在此处修改
\usecolortheme[named=主题色名称]{structure}

% TikZ库 - 完整功能集
\usetikzlibrary{
    shapes.geometric, 
    arrows, 
    positioning, 
    calc, 
    patterns, 
    decorations.pathreplacing, 
    mindmap, 
    calendar,
    trees,
    chains,
    scopes
}

% pgfplots设置
\pgfplotsset{compat=1.18}

% 自定义颜色定义 - 使用统一命名规范
% 主题色系 - 每周根据内容特点调整
\definecolor{PrimaryColor}{RGB}{64, 128, 255}    % 主色调
\definecolor{SecondaryColor}{RGB}{255, 140, 0}   % 辅助色
\definecolor{AccentColor}{RGB}{34, 139, 34}      % 强调色
\definecolor{WarningColor}{RGB}{255, 69, 0}      % 警告色
\definecolor{InfoColor}{RGB}{30, 144, 255}       % 信息色

% 课程信息设置 - 每周修改这部分
\title[第X周简短标题]{第X周：完整标题}
\subtitle{English Subtitle}
\author{AI驱动的传媒内容制作课程}
\institute{汕头大学}
\date{\today}

% 自定义命令定义
% 创建一致的强调框
\newcommand{\highlightbox}[2]{
    \begin{tcolorbox}[colback=#1!10, colframe=#1!70, rounded corners]
        #2
    \end{tcolorbox}
}

% 创建图标列表项
\newcommand{\iconitem}[2]{\item[#1] #2}

% 创建统一的互动框
\newcommand{\interactivebox}[2]{
    \begin{alertblock}{\faGamepad\ #1}
        #2
    \end{alertblock}
}

% 创建案例展示框
\newcommand{\casebox}[2]{
    \begin{exampleblock}{\faRocket\ #1}
        #2
    \end{exampleblock}
}

\begin{document}

% 标准页面结构

% 标题页
\begin{frame}
\titlepage
\end{frame}

% 目录页
\begin{frame}
\frametitle{课程大纲}
\tableofcontents
\end{frame}

% 第1部分：课程概述 - 模板示例
\section{课程概述}

\begin{frame}
\frametitle{课程概述示例页面}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{主要内容：}
\begin{itemize}
    \iconitem{\faLightbulb}{核心概念介绍}
    \iconitem{\faCog}{技术原理解析}
    \iconitem{\faRocket}{实践应用演示}
    \iconitem{\faCheck}{技能掌握验证}
\end{itemize}

\column{0.5\textwidth}
% TikZ图表示例
\begin{tikzpicture}[scale=0.8]
    \node[draw, rounded corners, fill=PrimaryColor!20] (concept) at (0,2) {核心概念};
    \node[draw, rounded corners, fill=SecondaryColor!20] (tech) at (0,1) {技术实现};
    \node[draw, rounded corners, fill=AccentColor!20] (practice) at (0,0) {实践应用};
    
    \draw[->, thick] (concept) -- (tech);
    \draw[->, thick] (tech) -- (practice);
\end{tikzpicture}
\end{columns}

\vspace{1em}

% 互动练习示例
\interactivebox{课堂互动}{
在这里添加互动练习内容，使用统一的互动框格式。
}

\end{frame}

% 第2部分：技术深入 - 带数据图表的模板
\section{技术深入}

\begin{frame}
\frametitle{数据可视化示例}

\begin{columns}[T]
\column{0.6\textwidth}
% pgfplots图表示例
\begin{tikzpicture}[scale=0.9]
    \begin{axis}[
        title={示例数据对比},
        ybar,
        xlabel={类别},
        ylabel={数值},
        symbolic x coords={A类,B类,C类},
        xtick=data,
        nodes near coords,
        ymin=0,
        legend pos=north west
    ]
    \addplot coordinates {(A类,30) (B类,50) (C类,40)};
    \addlegendentry{传统方法}
    
    \addplot coordinates {(A类,60) (B类,80) (C类,70)};
    \addlegendentry{AI方法}
    \end{axis}
\end{tikzpicture}

\column{0.4\textwidth}
\textbf{数据分析：}
\begin{itemize}
    \item AI方法效果提升：\textcolor{AccentColor}{\textbf{100\%}}
    \item 实施成本降低：\textcolor{AccentColor}{\textbf{30\%}}
    \item 用户满意度：\textcolor{AccentColor}{\textbf{95\%}}
\end{itemize}

\vspace{1em}
\casebox{成功案例}{
这里展示具体的成功案例，使用统一的案例展示框格式。
}
\end{columns}

\end{frame}

% 第3部分：实践应用
\section{实践应用}

\begin{frame}
\frametitle{思维导图示例}

% 复杂的思维导图示例
\begin{tikzpicture}[
    scale=0.8, 
    mindmap, 
    level 1 concept/.append style={sibling angle=90},
    level 2 concept/.append style={sibling angle=45}
]
    \node[concept, fill=PrimaryColor!50] {核心主题}
        child[concept color=SecondaryColor] {
            node[concept] {分支一}
            child { node[concept] {子概念1} }
            child { node[concept] {子概念2} }
        }
        child[concept color=AccentColor] {
            node[concept] {分支二}
            child { node[concept] {子概念3} }
            child { node[concept] {子概念4} }
        }
        child[concept color=InfoColor] {
            node[concept] {分支三}
            child { node[concept] {子概念5} }
            child { node[concept] {子概念6} }
        }
        child[concept color=WarningColor] {
            node[concept] {分支四}
            child { node[concept] {子概念7} }
            child { node[concept] {子概念8} }
        };
\end{tikzpicture}

\end{frame}

% 课程总结页面
\section{课程总结}

\begin{frame}
\frametitle{课程重点回顾}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{核心知识点：}
\begin{enumerate}
    \item 基本概念与原理
    \item 技术实现方法
    \item 实践应用技巧
    \item 质量评估标准
\end{enumerate}

\column{0.5\textwidth}
\textbf{技能掌握目标：}
\begin{itemize}
    \iconitem{\faCheck}{理论知识掌握}
    \iconitem{\faCheck}{工具使用熟练}
    \iconitem{\faCheck}{实践能力提升}
    \iconitem{\faCheck}{创新思维培养}
\end{itemize}
\end{columns}

\vspace{1em}

\highlightbox{PrimaryColor}{
\textbf{下周预告：}下一周将学习更高级的内容，包括...
}

\end{frame}

% 作业布置页面
\begin{frame}
\frametitle{课后作业}

\textbf{作业要求：}

\begin{itemize}
    \item[\faEdit] \textbf{理论作业}：完成相关概念的总结报告
    \item[\faLaptop] \textbf{实践作业}：使用工具完成指定任务
    \item[\faUsers] \textbf{小组作业}：团队协作完成综合项目
\end{itemize}

\vspace{1em}

\begin{alertblock}{\faCalendar\ 提交要求}
\begin{itemize}
    \item 提交时间：下周课前
    \item 提交格式：支持多种格式
    \item 评分标准：质量与创新并重
\end{itemize}
\end{alertblock}

\end{frame}

\end{document}