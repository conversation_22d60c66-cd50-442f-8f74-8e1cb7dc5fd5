\documentclass[aspectratio=169,xcolor=dvipsnames]{beamer}
\usepackage[UTF8]{ctex}
\usepackage{tikz}
\usepackage{pgfplots}
\usepackage{xcolor}
\usepackage{fontawesome5}
\usepackage{graphicx}
\usepackage{booktabs}
\usepackage{amsmath}
\usepackage{algorithm}
\usepackage{algorithmic}

% 主题设置
\usetheme{Madrid}
\usecolortheme[named=purple]{structure}

% TikZ库
\usetikzlibrary{shapes.geometric, arrows, positioning, calc, patterns, decorations.pathreplacing}

% 自定义颜色
\definecolor{AIBlue}{RGB}{64, 128, 255}
\definecolor{TechOrange}{RGB}{255, 165, 0}
\definecolor{DataGreen}{RGB}{34, 139, 34}

% 标题页设置
\title[智能内容创作基础]{第8周：智能内容创作基础}
\subtitle{AI-Powered Content Creation Fundamentals}
\author{AI驱动的传媒内容制作课程}
\institute{汕头大学}
\date{\today}

\begin{document}

% 标题页
\begin{frame}
\titlepage
\end{frame}

% 目录
\begin{frame}
\frametitle{课程大纲}
\tableofcontents
\end{frame}

% 第1部分：内容创作概述
\section{智能内容创作概述}

\begin{frame}
\frametitle{创作革命：AI重新定义内容创作}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{智能内容创作的定义：}
\begin{itemize}
    \item[\faRobot] \textbf{AI辅助创作}：利用人工智能技术辅助内容创作过程
    \item[\faSyncAlt] \textbf{人机协作}：结合人类创意和AI效率的协作模式
    \item[\faChartBar] \textbf{数据驱动}：基于数据分析和用户洞察的创作方法
    \item[\faBullseye] \textbf{个性化定制}：根据受众需求定制化的内容生产
\end{itemize}

\column{0.5\textwidth}
\begin{tikzpicture}[scale=0.8]
    % 创作流程图
    \node[draw, rounded corners, fill=AIBlue!20] (human) at (0,3) {人类创意};
    \node[draw, rounded corners, fill=TechOrange!20] (ai) at (0,2) {AI技术};
    \node[draw, rounded corners, fill=DataGreen!20] (data) at (0,1) {数据洞察};
    \node[draw, rounded corners, fill=purple!20] (content) at (0,0) {智能内容};
    
    % 连接线
    \draw[->, thick] (human) -- (content);
    \draw[->, thick] (ai) -- (content);
    \draw[->, thick] (data) -- (content);
    
    % 标注
    \node[right=0.5cm of content] {\small 个性化定制};
\end{tikzpicture}
\end{columns}

\vspace{1em}
\begin{alertblock}{\faLightbulb 互动练习}
\textbf{体验对比：}传统vs AI辅助创作效果差异
\begin{itemize}
    \item 模糊提示：「写一篇关于AI和新闻的文章」
    \item 优质提示：「你是10年经验科技记者...」
\end{itemize}
\end{alertblock}

\end{frame}

\begin{frame}
\frametitle{效率革命：量化数据展示}

\begin{columns}[T]
\column{0.6\textwidth}
\begin{tikzpicture}[scale=0.9]
    \begin{axis}[
        ybar,
        xlabel={创作阶段},
        ylabel={耗时(小时)},
        symbolic x coords={构思,写作,修改},
        xtick=data,
        nodes near coords,
        ymin=0,
        legend pos=north west
    ]
    \addplot coordinates {(构思,4) (写作,8) (修改,3)};
    \addlegendentry{传统方式}
    
    \addplot coordinates {(构思,0.5) (写作,2.5) (修改,0.5)};
    \addlegendentry{AI辅助}
    \end{axis}
\end{tikzpicture}

\column{0.4\textwidth}
\textbf{效率提升数据：}
\begin{itemize}
    \item 构思阶段：\textcolor{DataGreen}{\textbf{400-800\%}}
    \item 写作阶段：\textcolor{DataGreen}{\textbf{200-300\%}}
    \item 修改阶段：\textcolor{DataGreen}{\textbf{300-400\%}}
\end{itemize}

\vspace{1em}
\textbf{总体效果：}
\begin{itemize}
    \item 完整流程效率提升\textcolor{red}{\textbf{300\%}}
    \item 内容产出量提升\textcolor{red}{\textbf{500\%}}
    \item 写作成本降低\textcolor{red}{\textbf{60\%}}
\end{itemize}
\end{columns}

\end{frame}

\begin{frame}
\frametitle{AI内容创作技术架构}

\begin{tikzpicture}[
    box/.style={rectangle, draw, fill=blue!10, text width=3cm, text centered, rounded corners, minimum height=1cm},
    arrow/.style={->, thick}
]

% 输入理解层
\node[box, fill=red!10] (input) at (0,6) {输入理解层\\需求分析模块};
\node[box, fill=red!10] (analysis) at (4,6) {内容分析模块\\上下文理解};

% 知识处理层
\node[box, fill=orange!10] (knowledge) at (0,4) {知识处理层\\知识库管理};
\node[box, fill=orange!10] (resource) at (4,4) {语言资源库\\模板资源库};

% 生成处理层
\node[box, fill=green!10] (generate) at (0,2) {生成处理层\\创意生成引擎};
\node[box, fill=green!10] (content_gen) at (4,2) {内容生成引擎\\质量控制引擎};

% 交互优化层
\node[box, fill=purple!10] (interface) at (2,0) {交互优化层\\用户交互界面\\反馈学习机制};

% 连接线
\draw[arrow] (input) -- (knowledge);
\draw[arrow] (analysis) -- (resource);
\draw[arrow] (knowledge) -- (generate);
\draw[arrow] (resource) -- (content_gen);
\draw[arrow] (generate) -- (interface);
\draw[arrow] (content_gen) -- (interface);

\end{tikzpicture}

\end{frame}

% 第2部分：创作提示词设计
\section{创作提示词设计}

\begin{frame}
\frametitle{CRISPE框架在创作中的应用}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{提示词工程的重要性：}
\begin{itemize}
    \item[\faBullseye] \textbf{创作方向控制}：精确指导AI的创作方向
    \item[\faChartBar] \textbf{质量保证工具}：确保输出质量
    \item[\faBolt] \textbf{效率提升手段}：提高一次成功率
    \item[\faPalette] \textbf{创意激发媒介}：激发AI创作潜能
\end{itemize}

\column{0.5\textwidth}
\begin{tikzpicture}[scale=0.7]
    % CRISPE可视化
    \node[draw, circle, fill=blue!20, minimum size=2cm] at (0,0) {CRISPE\\框架};
    
    % 五个要素
    \node[draw, rounded corners, fill=red!20, text width=1.5cm, text centered] at (-2.5,1.5) {C\\Capacity};
    \node[draw, rounded corners, fill=orange!20, text width=1.5cm, text centered] at (2.5,1.5) {R\\Insight};
    \node[draw, rounded corners, fill=yellow!20, text width=1.5cm, text centered] at (-2.5,-1.5) {I\\Statement};
    \node[draw, rounded corners, fill=green!20, text width=1.5cm, text centered] at (2.5,-1.5) {S\\Personality};
    \node[draw, rounded corners, fill=purple!20, text width=1.5cm, text centered] at (0,2.5) {P\\Experiment};
    
    % 连接线
    \foreach \angle in {90,45,-45,-90,-135}
        \draw[thick,->] (0,0) -- (\angle:2);
\end{tikzpicture}
\end{columns}

\vspace{1em}
\begin{exampleblock}{\faUsers 实战演示}
\textbf{新闻编辑招聘文案对比：}
\begin{itemize}
    \item 传统方式：「帮我写个招聘新闻编辑的文案」
    \item CRISPE方式：详细角色+背景+任务+风格+实验
\end{itemize}
\end{exampleblock}

\end{frame}

\begin{frame}
\frametitle{风格控制的维度与技巧}

\begin{columns}[T]
\column{0.4\textwidth}
\textbf{语言正式程度：}
\begin{itemize}
    \item \textbf{正式风格}：学术论文、政府公文
    \item \textbf{半正式风格}：商业报告、新闻报道
    \item \textbf{非正式风格}：社交媒体、个人博客
\end{itemize}

\vspace{1em}
\textbf{情感色彩控制：}
\begin{itemize}
    \item \textbf{客观中性}：新闻报道
    \item \textbf{积极正面}：营销文案
    \item \textbf{严肃权威}：专业分析
    \item \textbf{轻松幽默}：娱乐内容
\end{itemize}

\column{0.6\textwidth}
\begin{tikzpicture}[scale=0.8]
    % 风格维度图
    \draw[->,thick] (0,0) -- (6,0) node[right] {正式度};
    \draw[->,thick] (0,0) -- (0,4) node[above] {情感强度};
    
    % 象限标注
    \node[text width=2cm, text centered] at (1.5,3) {\small 严肃\\权威};
    \node[text width=2cm, text centered] at (4.5,3) {\small 正式\\积极};
    \node[text width=2cm, text centered] at (1.5,1) {\small 轻松\\亲和};
    \node[text width=2cm, text centered] at (4.5,1) {\small 专业\\客观};
    
    % 应用场景点
    \node[circle, fill=red, minimum size=3pt] at (2,0.5) {};
    \node[below] at (2,0.5) {\tiny 社交媒体};
    
    \node[circle, fill=blue, minimum size=3pt] at (5,2) {};
    \node[right] at (5,2) {\tiny 商业报告};
    
    \node[circle, fill=green, minimum size=3pt] at (1.5,3.2) {};
    \node[above] at (1.5,3.2) {\tiny 学术论文};
\end{tikzpicture}
\end{columns}

\end{frame}

% 第3部分：AI写作工具实践
\section{AI写作工具实践}

\begin{frame}
\frametitle{主流AI写作工具对比}

\begin{table}[H]
\centering
\scalebox{0.8}{
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{工具} & \textbf{中文能力} & \textbf{长文本} & \textbf{专业性} & \textbf{成本} \\
\hline
ChatGPT-4 & ★★★★ & ★★★★ & ★★★★★ & 高 \\
\hline
Claude 3 & ★★★★ & ★★★★★ & ★★★★★ & 高 \\
\hline
文心一言 & ★★★★★ & ★★★ & ★★★★ & 中 \\
\hline
通义千问 & ★★★★★ & ★★★ & ★★★★ & 中 \\
\hline
Grammarly & ★★ & ★★★ & ★★★ & 低 \\
\hline
\end{tabular}
}
\end{table}

\vspace{1em}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{工具选择策略：}
\begin{itemize}
    \item[\faBullseye] 需求匹配：功能与需求的匹配度
    \item[\faDollarSign] 成本考虑：总体拥有成本
    \item[\faLock] 安全合规：数据安全和政策合规
    \item[\faCogs] 生态整合：与现有工具的整合
\end{itemize}

\column{0.5\textwidth}
\textbf{应用场景建议：}
\begin{itemize}
    \item \textbf{学术写作}：Claude 3 (长文本)
    \item \textbf{新闻报道}：ChatGPT-4 (全面性)
    \item \textbf{中文内容}：文心一言/通义千问
    \item \textbf{语法检查}：Grammarly
\end{itemize}
\end{columns}

\end{frame}

\begin{frame}
\frametitle{工作流集成实践}

\begin{tikzpicture}[
    node distance=2cm and 3cm,
    process/.style={rectangle, draw, fill=blue!10, text width=2.5cm, text centered, minimum height=1cm},
    tool/.style={rectangle, draw, fill=orange!10, text width=2cm, text centered, minimum height=0.8cm},
    arrow/.style={->, thick}
]

% 工作流程
\node[process] (need) {需求分析};
\node[process, right=of need] (plan) {创意策划};
\node[process, right=of plan] (create) {内容制作};
\node[process, right=of create] (review) {审核发布};

% 对应工具
\node[tool, below=1cm of need] {用户调研\\ChatGPT分析};
\node[tool, below=1cm of plan] {思维导图\\AI创意助手};
\node[tool, below=1cm of create] {AI写作\\图像生成};
\node[tool, below=1cm of review] {语法检查\\发布管理};

% 连接线
\draw[arrow] (need) -- (plan);
\draw[arrow] (plan) -- (create);
\draw[arrow] (create) -- (review);

% 工具连接
\foreach \x in {need, plan, create, review}
    \draw[arrow, dashed] (\x) -- ([yshift=1cm]\x |- 0,-2);

\end{tikzpicture}

\vspace{1em}

\begin{alertblock}{\faExclamationTriangle 最佳实践提醒}
\begin{itemize}
    \item 建立标准化的提示词模板库
    \item 设置多层次质量检查机制
    \item 保持人工审核关键环节
    \item 定期评估和优化工作流程
\end{itemize}
\end{alertblock}

\end{frame}

% 第4部分：实践案例与效果评估
\section{实践案例与效果评估}

\begin{frame}
\frametitle{新闻媒体工作流案例}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{传统新闻生产流程：}
\begin{enumerate}
    \item \textbf{信息收集}：手动搜集，耗时4-6小时
    \item \textbf{内容创作}：人工写作，耗时6-8小时
    \item \textbf{编辑发布}：多轮修改，耗时2-3小时
    \item \textbf{效果监控}：人工分析，耗时1-2小时
\end{enumerate}

\textbf{总耗时：13-19小时}

\column{0.5\textwidth}
\textbf{AI辅助新闻生产流程：}
\begin{enumerate}
    \item \textbf{智能收集}：AI监控+整理，耗时1-2小时
    \item \textbf{AI辅助创作}：AI生成+人工编辑，耗时2-3小时
    \item \textbf{智能编辑}：AI检查+人工审核，耗时0.5-1小时
    \item \textbf{自动监控}：AI分析+报告，耗时0.5小时
\end{enumerate}

\textbf{总耗时：4-6.5小时}
\end{columns}

\vspace{1em}

\begin{tikzpicture}[scale=0.8]
    % 效率对比图
    \draw[fill=red!20] (0,0) rectangle (6,1) node[pos=0.5] {传统流程：13-19小时};
    \draw[fill=green!20] (0,-1.5) rectangle (2.5,-0.5) node[pos=0.5] {AI流程：4-6.5小时};
    
    % 箭头和标注
    \draw[->, thick, red] (6.5,0.5) -- (8,0.5) node[right] {耗时长，效率低};
    \draw[->, thick, green] (3,1) -- (8,1) node[right] {效率提升200-300\%};
\end{tikzpicture}

\end{frame}

\begin{frame}
\frametitle{ROI评估与成本效益分析}

\begin{columns}[T]
\column{0.6\textwidth}
\begin{tikzpicture}[scale=0.9]
    % ROI计算图表
    \begin{axis}[
        title={三年投资回报分析},
        xlabel={年份},
        ylabel={金额(万元)},
        xmin=0, xmax=4,
        ymin=-50, ymax=100,
        xtick={1,2,3},
        legend pos=north west,
        grid=major
    ]
    
    % 投资成本线
    \addplot[red, thick, mark=square] coordinates {(1,-20) (2,-15) (3,-10)};
    \addlegendentry{年度投资成本}
    
    % 收益线
    \addplot[green, thick, mark=circle] coordinates {(1,42) (2,50) (3,58)};
    \addlegendentry{年度净收益}
    
    % 累计ROI
    \addplot[blue, thick, mark=triangle] coordinates {(1,22) (2,57) (3,105)};
    \addlegendentry{累计净收益}
    
    \end{axis}
\end{tikzpicture}

\column{0.4\textwidth}
\textbf{投资结构：}
\begin{itemize}
    \item 工具成本：8.5万/年
    \item 培训成本：5万/年
    \item 技术支持：3万/年
    \item 管理成本：3.5万/年
\end{itemize}

\vspace{0.5em}
\textbf{收益构成：}
\begin{itemize}
    \item 效率提升：29万/年
    \item 质量提升：15万/年
    \item 成本降低：18万/年
\end{itemize}

\vspace{0.5em}
\textbf{关键指标：}
\begin{itemize}
    \item[\faArrowUp] ROI：\textcolor{red}{\textbf{210\%}}
    \item[\faClock] 投资回收期：\textcolor{green}{\textbf{5.7个月}}
\end{itemize}
\end{columns}

\end{frame}

% 课程总结
\section{课程总结与展望}

\begin{frame}
\frametitle{课程重点回顾}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{核心知识点：}
\begin{enumerate}
    \item \textbf{智能创作概念}：AI与人类创意的深度融合
    \item \textbf{技术架构}：四层架构体系
    \item \textbf{提示词工程}：CRISPE框架应用
    \item \textbf{工具选择}：基于需求的工具评估
    \item \textbf{工作流优化}：系统化的流程设计
\end{enumerate}

\column{0.5\textwidth}
\textbf{实践技能：}
\begin{itemize}
    \item[\faCheck] 掌握AI写作工具的使用
    \item[\faCheck] 设计有效的提示词
    \item[\faCheck] 建立高效的创作流程
    \item[\faCheck] 进行成本效益评估
    \item[\faCheck] 实现人机协作优化
\end{itemize}
\end{columns}

\vspace{1em}

\begin{exampleblock}{\faRocket 下周预告：第9周 - 创意生成选题策划}
\textbf{学习重点：}
\begin{itemize}
    \item 创意思维模式与方法
    \item AI创意激发技术
    \item 选题策划的系统化方法
    \item 创意评估与优化策略
\end{itemize}
\end{exampleblock}

\end{frame}

\begin{frame}
\frametitle{课后实践作业}

\textbf{作业一：工具对比评估（40分）}
\begin{itemize}
    \item 选择3个AI写作工具进行深度体验
    \item 从功能、易用性、成本、效果四个维度评估
    \item 撰写1500字的对比评估报告
\end{itemize}

\vspace{0.5em}

\textbf{作业二：提示词设计实践（40分）}
\begin{itemize}
    \item 基于CRISPE框架设计5个不同场景的提示词
    \item 包含：新闻报道、营销文案、学术写作、社交媒体、商业报告
    \item 测试效果并记录优化过程
\end{itemize}

\vspace{0.5em}

\textbf{作业三：个人工作流设计（20分）}
\begin{itemize}
    \item 设计适合自己的AI辅助创作工作流
    \item 绘制流程图并说明工具选择理由
    \item 预估效率提升和成本效益
\end{itemize}

\vspace{1em}

\begin{alertblock}{\faCalendar 提交要求}
下周课前提交，支持多种格式：Word文档、PPT演示、思维导图等
\end{alertblock}

\end{frame}

\end{document}