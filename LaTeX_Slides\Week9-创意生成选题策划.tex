\documentclass[aspectratio=169,xcolor=dvipsnames]{beamer}
\usepackage[UTF8]{ctex}
\usepackage{tikz}
\usepackage{pgfplots}
\usepackage{xcolor}
\usepackage{fontawesome5}
\usepackage{graphicx}
\usepackage{booktabs}
\usepackage{amsmath}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{forest}
\usepackage{tcolorbox}

% 主题设置
\usetheme{Madrid}
\usecolortheme[named=orange]{structure}

% TikZ库
\usetikzlibrary{shapes.geometric, arrows, positioning, calc, patterns, decorations.pathreplacing, mindmap, calendar}

% 自定义颜色
\definecolor{CreativeOrange}{RGB}{255, 140, 0}
\definecolor{IdeaYellow}{RGB}{255, 215, 0}
\definecolor{BrainPurple}{RGB}{147, 112, 219}

\title[创意生成选题策划]{第9周：创意生成选题策划}
\subtitle{Creative Content Generation and Topic Planning}
\author{AI驱动的传媒内容制作课程}
\institute{汕头大学}
\date{\today}

\begin{document}

\begin{frame}
\titlepage
\end{frame}

\begin{frame}
\frametitle{课程大纲}
\tableofcontents
\end{frame}

\section{创意生成概述}

\begin{frame}
\frametitle{创意之源：AI时代的内容创意生成}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{创意生成的定义：}
\begin{itemize}
    \item[\faLightbulb] \textbf{创新思维}：产生新颖、独特、有价值的想法
    \item[\faSyncAlt] \textbf{组合创新}：将已有元素以新方式组合
    \item[\faBullseye] \textbf{问题解决}：针对特定需求提出创造性解决方案
    \item[\faStar] \textbf{价值创造}：通过创意思维创造新价值
\end{itemize}

\column{0.5\textwidth}
\begin{tikzpicture}[scale=0.8, mindmap, 
    level 1 concept/.append style={sibling angle=90},
    level 2 concept/.append style={sibling angle=45}]
    
    \node[concept, fill=CreativeOrange!50] {创意生成}
        child[concept color=IdeaYellow] {
            node[concept] {差异化竞争}
            child { node[concept] {独特性} }
            child { node[concept] {吸引力} }
        }
        child[concept color=BrainPurple] {
            node[concept] {用户体验}
            child { node[concept] {新鲜感} }
            child { node[concept] {共鸣感} }
        }
        child[concept color=green] {
            node[concept] {商业价值}
            child { node[concept] {流量增长} }
            child { node[concept] {品牌资产} }
        }
        child[concept color=red] {
            node[concept] {AI协作}
            child { node[concept] {规模化} }
            child { node[concept] {个性化} }
        };
\end{tikzpicture}
\end{columns}

\vspace{1em}
\begin{exampleblock}{\faGamepad 互动游戏}
\textbf{创意联想挑战：}给定关键词「智能手机」，2分钟内生成尽可能多的创意角度
\begin{itemize}
    \item 小组竞赛：每组推选最佳创意
    \item AI辅助：使用ChatGPT生成创意对比
\end{itemize}
\end{exampleblock}

\end{frame}

\begin{frame}
\frametitle{创意生成的价值量化分析}

\begin{columns}[T]
\column{0.6\textwidth}
\begin{tikzpicture}[scale=0.9]
    \begin{axis}[
        title={创意内容vs常规内容效果对比},
        ybar,
        xlabel={指标类型},
        ylabel={提升百分比(\%)},
        symbolic x coords={点击率,分享率,认知度,参与度},
        xtick=data,
        nodes near coords,
        ymin=0,
        ymax=500,
        legend pos=north west,
        bar width=15pt
    ]
    \addplot[fill=CreativeOrange!70] coordinates {
        (点击率,250) (分享率,400) (认知度,150) (参与度,250)
    };
    \addlegendentry{创意内容提升幅度}
    \end{axis}
\end{tikzpicture}

\column{0.4\textwidth}
\textbf{数据支撑：}
\begin{itemize}
    \item 点击率提升：\textcolor{red}{\textbf{200-300\%}}
    \item 分享率提升：\textcolor{red}{\textbf{400\%}}
    \item 品牌认知度：\textcolor{red}{\textbf{150\%}}
    \item 用户参与度：\textcolor{red}{\textbf{250\%}}
\end{itemize}

\vspace{1em}
\textbf{AI创意生成优势：}
\begin{itemize}
    \item[\faRocket] 规模化生产：7×24小时产出
    \item[\faPalette] 多样性保证：多角度创意
    \item[\faChartLine] 质量稳定：基线质量保证
    \item[\faUser] 个性化定制：精准匹配需求
\end{itemize}
\end{columns}

\end{frame}

\section{创意思维模式与方法}

\begin{frame}
\frametitle{发散思维：头脑风暴与思维导图}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{头脑风暴法进化：}
\begin{enumerate}
    \item \textbf{传统方式}：人工讨论，思维局限
    \item \textbf{AI增强版}：
        \begin{itemize}
            \item[\faRobot] AI快速生成初始想法
            \item[\faLink] 基于关联生成启发
            \item[\faSort] 自动分类整理想法
            \item[\faStarHalfAlt] 初步质量评估
        \end{itemize}
\end{enumerate}

\vspace{1em}
\textbf{六顶思考帽AI模拟：}
\begin{itemize}
    \item[\faHatCowboy] \textbf{白帽}：客观事实数据
    \item[\faHeartbeat] \textbf{红帽}：情感直觉判断
    \item[\faExclamationTriangle] \textbf{黑帽}：风险批判分析
    \item[\faSun] \textbf{黄帽}：积极价值发现
    \item[\faLeaf] \textbf{绿帽}：创新可能探索
    \item[\faCog] \textbf{蓝帽}：过程控制管理
\end{itemize}

\column{0.5\textwidth}
\begin{tikzpicture}[scale=0.7, level distance=1.5cm, sibling distance=1cm, 
    edge from parent/.style={draw,thick}]
\node[draw, rounded corners, fill=CreativeOrange!30, text width=2cm, text centered] {环保主题\\创意策划}
    child { 
        node[fill=IdeaYellow!30, text width=1.5cm, text centered] {内容形式}
        child { node[fill=white, text width=1cm, text centered] {短视频} }
        child { node[fill=white, text width=1cm, text centered] {互动H5} }
        child { node[fill=white, text width=1cm, text centered] {游戏化} }
    }
    child { 
        node[fill=BrainPurple!30, text width=1.5cm, text centered] {传播渠道}
        child { node[fill=white, text width=1cm, text centered] {抖音} }
        child { node[fill=white, text width=1cm, text centered] {微博} }
        child { node[fill=white, text width=1cm, text centered] {B站} }
    }
    child { 
        node[fill=green!30, text width=1.5cm, text centered] {创意角度}
        child { node[fill=white, text width=1cm, text centered] {生活方式} }
        child { node[fill=white, text width=1cm, text centered] {技术创新} }
        child { node[fill=white, text width=1cm, text centered] {社会责任} }
    };
\end{tikzpicture}
\end{columns}

\end{frame}

\begin{frame}
\frametitle{收敛思维：SCAMPER技法与强制关联}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{SCAMPER技法AI辅助：}
\begin{itemize}
    \item[\faExchangeAlt] \textbf{S-替代}：用AI寻找替代方案
    \item[\faLink] \textbf{C-结合}：智能组合不同元素
    \item[\faAdjust] \textbf{A-适应}：适应新场景应用
    \item[\faEdit] \textbf{M-修改}：优化改进现有方案
    \item[\faRecycle] \textbf{P-其他用途}：挖掘新用途
    \item[\faMinusCircle] \textbf{E-消除}：简化去除冗余
    \item[\faRedo] \textbf{R-逆转}：反向思维创新
\end{itemize}

\column{0.5\textwidth}
\textbf{强制关联法实践：}

\begin{tikzpicture}[scale=0.8]
    % 随机词汇库
    \node[draw, rounded corners, fill=IdeaYellow!30] (target) at (0,2) {目标主题\\新产品发布};
    
    \node[draw, circle, fill=red!20] (word1) at (-2,0) {雨伞};
    \node[draw, circle, fill=blue!20] (word2) at (0,0) {音乐};
    \node[draw, circle, fill=green!20] (word3) at (2,0) {游戏};
    
    % 关联线
    \draw[->, thick, red] (word1) -- (target) node[midway, left, text width=2cm] {\tiny 保护功能\\安全理念};
    \draw[->, thick, blue] (word2) -- (target) node[midway, above, text width=2cm] {\tiny 节奏感\\情感共鸣};
    \draw[->, thick, green] (word3) -- (target) node[midway, right, text width=2cm] {\tiny 互动体验\\娱乐性};
    
    % 创意输出
    \node[draw, rounded corners, fill=CreativeOrange!30, text width=3cm, text centered] at (0,-2) {创意方案\\「保护伞式」产品发布\\结合音乐和游戏互动};
\end{tikzpicture}
\end{columns}

\vspace{1em}
\begin{alertblock}{\faBrain 现场练习}
\textbf{强制关联创意生成：}
\begin{enumerate}
    \item 目标：为「在线教育平台」设计推广创意
    \item 随机词汇：「咖啡」「旅行」「魔法」
    \item 任务：3分钟内生成创意方案，小组分享
\end{enumerate}
\end{alertblock}

\end{frame}

\begin{frame}
\frametitle{系统思维：设计思维与TRIZ理论}

\begin{columns}[T]
\column{0.6\textwidth}
\textbf{设计思维五阶段AI辅助：}

\begin{tikzpicture}[
    process/.style={rectangle, draw, fill=blue!10, text width=2cm, text centered, minimum height=1cm},
    arrow/.style={->, thick}
]
    \node[process, fill=red!20] (empathize) at (0,4) {共情\\Empathize};
    \node[process, fill=orange!20] (define) at (2,4) {定义\\Define};
    \node[process, fill=yellow!20] (ideate) at (4,4) {构思\\Ideate};
    \node[process, fill=green!20] (prototype) at (2,2) {原型\\Prototype};
    \node[process, fill=purple!20] (test) at (0,2) {测试\\Test};
    
    % 连接线
    \draw[arrow] (empathize) -- (define);
    \draw[arrow] (define) -- (ideate);
    \draw[arrow] (ideate) -- (prototype);
    \draw[arrow] (prototype) -- (test);
    \draw[arrow] (test) -- (empathize);
    
    % AI辅助标注
    \node[text width=1.5cm, text centered] at (5.5,4) {\small AI用户\\洞察分析};
    \node[text width=1.5cm, text centered] at (5.5,2) {\small AI快速\\原型生成};
\end{tikzpicture}

\column{0.4\textwidth}
\textbf{TRIZ理论AI应用：}
\begin{itemize}
    \item[\faCogs] \textbf{自动建模}：问题转TRIZ模型
    \item[\faSearch] \textbf{矛盾识别}：智能识别矛盾点
    \item[\faLightbulb] \textbf{原理推荐}：推荐发明原理
    \item[\faWrench] \textbf{方案生成}：基于原理生成方案
\end{itemize}

\vspace{1em}
\textbf{40个发明原理示例：}
\begin{enumerate}
    \item 分割原理
    \item 抽取原理
    \item 局部质量
    \item 非对称原理
    \item 合并原理
    \item[...] 等等
\end{enumerate}
\end{columns}

\vspace{1em}
\begin{exampleblock}{\faRocket 案例演示}
\textbf{TRIZ解决矛盾：}如何让在线课程既「专业深入」又「轻松易懂」？
\begin{itemize}
    \item 矛盾：专业性 vs 易理解性
    \item TRIZ原理：分割原理 + 层次原理
    \item 解决方案：分层设计，初级版+专业版
\end{itemize}
\end{exampleblock}

\end{frame}

\section{AI创意激发技术}

\begin{frame}
\frametitle{大语言模型创意生成机制}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{技术原理：}
\begin{itemize}
    \item[\faDatabase] \textbf{预训练知识}：海量跨领域数据
    \item[\faNetworkWired] \textbf{模式识别}：识别创意模式
    \item[\faRandom] \textbf{联想生成}：基于输入触发联想
    \item[\faPuzzlePiece] \textbf{组合创新}：创新元素组合
\end{itemize}

\vspace{1em}
\textbf{创意激发提示模板：}
\begin{tcolorbox}[colback=IdeaYellow!10, colframe=CreativeOrange]
\small
"请为[主题]生成10个创新想法，要求：
\begin{enumerate}
    \item 具有新颖性和独特性
    \item 具备实际可行性  
    \item 能吸引目标受众
    \item 符合当前趋势
\end{enumerate}
请从技术、模式、体验等角度思考。"
\end{tcolorbox}

\column{0.5\textwidth}
\begin{tikzpicture}[scale=0.8]
    % AI创意生成流程
    \node[draw, rounded corners, fill=BrainPurple!20] (input) at (0,4) {输入主题};
    \node[draw, rounded corners, fill=IdeaYellow!20] (analysis) at (0,3) {语义分析};
    \node[draw, rounded corners, fill=CreativeOrange!20] (associate) at (0,2) {联想激发};
    \node[draw, rounded corners, fill=green!20] (combine) at (0,1) {创意组合};
    \node[draw, rounded corners, fill=red!20] (output) at (0,0) {创意输出};
    
    % 连接线
    \foreach \i/\j in {input/analysis, analysis/associate, associate/combine, combine/output}
        \draw[->, thick] (\i) -- (\j);
    
    % 侧边标注
    \node[right=1cm of analysis] {\small 理解主题内涵};
    \node[right=1cm of associate] {\small 跨域知识链接};
    \node[right=1cm of combine] {\small 新颖元素组合};
\end{tikzpicture}
\end{columns}

\vspace{1em}
\begin{exampleblock}{\faCode 实践演示}
\textbf{反向思维提示：}
\small
"运用反向思维为[短视频平台]生成创意：
\begin{enumerate}
    \item 如果做相反的事情会怎样？→ 长视频平台
    \item 如果消除关键要素会怎样？→ 无声视频
    \item 如果颠倒流程会怎样？→ 先评论后观看
\end{enumerate}
基于反向思考生成具体创意方案。"
\end{exampleblock}

\end{frame}

\begin{frame}
\frametitle{多模态创意融合技术}

\begin{columns}[T]
\column{0.6\textwidth}
\textbf{文本+图像创意融合：}

\begin{tikzpicture}[scale=0.8, 
    box/.style={rectangle, draw, rounded corners, text width=2cm, text centered, minimum height=1cm}]
    
    % 输入层
    \node[box, fill=blue!20] (text) at (0,3) {文本内容};
    \node[box, fill=green!20] (image) at (3,3) {图像素材};
    
    % 分析层
    \node[box, fill=orange!20] (text_ana) at (0,2) {语义分析};
    \node[box, fill=orange!20] (image_ana) at (3,2) {视觉分析};
    
    % 融合层
    \node[box, fill=purple!20] (fusion) at (1.5,1) {多模态融合};
    
    % 输出层
    \node[box, fill=red!20] (output) at (1.5,0) {创意方案};
    
    % 连接线
    \draw[->, thick] (text) -- (text_ana);
    \draw[->, thick] (image) -- (image_ana);
    \draw[->, thick] (text_ana) -- (fusion);
    \draw[->, thick] (image_ana) -- (fusion);
    \draw[->, thick] (fusion) -- (output);
\end{tikzpicture}

\vspace{0.5em}
\textbf{应用场景：}
\begin{itemize}
    \item 广告创意：产品图片+文案创作
    \item 内容策划：视觉素材+主题策划
    \item 品牌故事：品牌视觉+故事叙述
\end{itemize}

\column{0.4\textwidth}
\textbf{知识图谱创意发现：}

\begin{tikzpicture}[scale=0.7]
    % 知识节点
    \node[circle, draw, fill=red!30] (tech) at (0,2) {科技};
    \node[circle, draw, fill=blue!30] (art) at (2,2) {艺术};
    \node[circle, draw, fill=green!30] (edu) at (1,0) {教育};
    \node[circle, draw, fill=yellow!30] (game) at (-1,0) {游戏};
    
    % 关联线
    \draw[thick] (tech) -- (art) node[midway, above] {\tiny 科技艺术};
    \draw[thick] (tech) -- (edu) node[midway, right] {\tiny 在线教育};
    \draw[thick] (art) -- (edu) node[midway, right] {\tiny 艺术教育};
    \draw[thick] (game) -- (edu) node[midway, below] {\tiny 游戏化学习};
    \draw[thick, dashed, red] (tech) -- (game) node[midway, left] {\tiny 创新连接};
    
    % 创意发现
    \node[below=1cm of edu, text width=3cm, text centered] {\small 发现：科技+游戏\\=VR教育游戏};
\end{tikzpicture}

\vspace{0.5em}
\textbf{发现机制：}
\begin{itemize}
    \item[\faSearch] 路径探索：概念连接路径
    \item[\faEye] 关联发现：意外概念关联
    \item[\faMapMarked] 空白识别：知识网络空白
    \item[\faBridge] 桥接创新：概念桥接
\end{itemize}
\end{columns}

\end{frame}

\begin{frame}
\frametitle{创意工作流与质量控制}

\begin{columns}[T]
\column{0.6\textwidth}
\textbf{四阶段创意工作流：}

\begin{tikzpicture}[
    phase/.style={rectangle, draw, fill=blue!10, text width=2.5cm, text centered, rounded corners},
    arrow/.style={->, thick}
]
    \node[phase, fill=red!20] (discover) at (0,3) {创意发现阶段\\问题定义\\AI辅助分析};
    \node[phase, fill=orange!20] (generate) at (3,3) {创意生成阶段\\多轮迭代\\AI生成策略};
    \node[phase, fill=green!20] (evaluate) at (0,1.5) {创意评估阶段\\评估维度\\AI辅助评分};
    \node[phase, fill=purple!20] (implement) at (3,1.5) {创意实施阶段\\方案细化\\AI实施支持};
    
    % 连接线
    \draw[arrow] (discover) -- (generate);
    \draw[arrow] (generate) -- (implement);
    \draw[arrow] (implement) -- (evaluate);
    \draw[arrow] (evaluate) -- (discover);
\end{tikzpicture}

\column{0.4\textwidth}
\textbf{创意质量评估标准：}
\begin{itemize}
    \item[\faStar] \textbf{创新性评估}
        \begin{itemize}
            \item 独特性：差异程度
            \item 原创性：原创程度
            \item 突破性：突破程度
        \end{itemize}
    \item[\faCog] \textbf{实用性评估}
        \begin{itemize}
            \item 可行性：技术可行
            \item 适用性：场景匹配
            \item 效果性：预期效果
        \end{itemize}
    \item[\faDollarSign] \textbf{价值性评估}
        \begin{itemize}
            \item 商业价值：潜在收益
            \item 社会价值：积极影响
            \item 品牌价值：形象提升
        \end{itemize}
\end{itemize}
\end{columns}

\vspace{1em}
\begin{exampleblock}{\faClipboardCheck 质量控制流程}
\textbf{三层筛选机制：}
\begin{enumerate}
    \item \textbf{初步筛选}：基本要求检查，AI自动筛选
    \item \textbf{深度评估}：专家评审，用户测试验证
    \item \textbf{持续优化}：反馈收集，改进方案制定
\end{enumerate}
\end{exampleblock}

\end{frame}

\section{选题策划实践}

\begin{frame}
\frametitle{传媒选题策划系统化方法}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{选题策划六步法：}
\begin{enumerate}
    \item[\faSearch] \textbf{趋势分析}：热点监控+数据分析
    \item[\faUsers] \textbf{受众研究}：用户画像+需求洞察
    \item[\faLightbulb] \textbf{创意生成}：AI辅助+人工筛选
    \item[\faBalanceScale] \textbf{价值评估}：多维度评估体系
    \item[\faCalendarAlt] \textbf{时机选择}：发布时机优化
    \item[\faChartLine] \textbf{效果预测}：传播效果建模
\end{enumerate}

\vspace{1em}
\textbf{AI工具配合：}
\begin{itemize}
    \item 趋势监控：Google Trends + AI分析
    \item 用户洞察：数据挖掘 + AI建模
    \item 创意生成：ChatGPT + 创意模板
    \item 效果预测：机器学习 + 历史数据
\end{itemize}

\column{0.5\textwidth}
\begin{tikzpicture}[scale=0.7]
    % 选题策划流程图
    \node[draw, circle, fill=red!20] (trend) at (0,4) {\small 趋势\\分析};
    \node[draw, circle, fill=orange!20] (user) at (2,3) {\small 用户\\研究};
    \node[draw, circle, fill=yellow!20] (idea) at (3,1) {\small 创意\\生成};
    \node[draw, circle, fill=green!20] (eval) at (2,-1) {\small 价值\\评估};
    \node[draw, circle, fill=blue!20] (time) at (0,-1) {\small 时机\\选择};
    \node[draw, circle, fill=purple!20] (predict) at (-2,1) {\small 效果\\预测};
    
    % 连接线
    \draw[->, thick] (trend) -- (user);
    \draw[->, thick] (user) -- (idea);
    \draw[->, thick] (idea) -- (eval);
    \draw[->, thick] (eval) -- (time);
    \draw[->, thick] (time) -- (predict);
    \draw[->, thick] (predict) -- (trend);
    
    % 中心
    \node[draw, rounded corners, fill=CreativeOrange!30] at (1,1.5) {\small 最优\\选题};
\end{tikzpicture}
\end{columns}

\vspace{1em}
\begin{alertblock}{\faExclamationTriangle 选题避坑指南}
\textbf{常见错误：}
\begin{itemize}
    \item 只追热点，忽视品牌调性
    \item 创意新颖，但执行困难
    \item 受众定位不清，传播效果差
    \item 时机把握不准，错过最佳窗口
\end{itemize}
\end{alertblock}

\end{frame}

\begin{frame}
\frametitle{案例实战：环保主题创意策划}

\textbf{案例背景：}为环保NGO组织策划「地球日」主题传播活动

\vspace{0.5em}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{AI辅助分析过程：}

\begin{tcolorbox}[colback=green!5, colframe=green!50]
\footnotesize
\textbf{步骤1：趋势分析}
\begin{itemize}
    \item AI分析：环保话题热度上升
    \item 关键词：可持续发展、碳中和
    \item 用户关注：个人行动、企业责任
\end{itemize}

\textbf{步骤2：受众洞察}
\begin{itemize}
    \item 主要群体：18-35岁城市用户
    \item 行为特征：关注生活品质，愿意分享
    \item 痛点：不知如何参与环保行动
\end{itemize}

\textbf{步骤3：创意生成}
\begin{itemize}
    \item AI生成30个创意方向
    \item 筛选出5个可行性强的方案
    \item 重点：个人参与 + 社交传播
\end{itemize}
\end{tcolorbox}

\column{0.5\textwidth}
\textbf{最终创意方案：}

\begin{tikzpicture}[scale=0.8]
    % 创意方案展示
    \node[draw, rounded corners, fill=CreativeOrange!30, text width=4cm, text centered] (main) at (0,3) {\textbf{「21天绿色挑战」}\\个人环保行动计划};
    
    \node[draw, fill=IdeaYellow!20, text width=1.8cm, text centered] (content) at (-2,1) {内容形式\\日签海报\\进度打卡\\分享机制};
    
    \node[draw, fill=BrainPurple!20, text width=1.8cm, text centered] (channel) at (2,1) {传播渠道\\微信小程序\\微博话题\\抖音挑战};
    
    \node[draw, fill=green!20, text width=1.8cm, text centered] (interact) at (0,-0.5) {互动设计\\好友PK\\排行榜\\奖励机制};
    
    % 连接线
    \draw[->, thick] (main) -- (content);
    \draw[->, thick] (main) -- (channel);
    \draw[->, thick] (main) -- (interact);
\end{tikzpicture}

\vspace{0.5em}
\textbf{预期效果：}
\begin{itemize}
    \item 参与用户：10万+
    \item 话题阅读：1000万+
    \item 品牌曝光：显著提升
\end{itemize}
\end{columns}

\end{frame}

\section{课程总结与实践}

\begin{frame}
\frametitle{创意生成核心要点回顾}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{理论知识掌握：}
\begin{itemize}
    \item[\faCheck] \textbf{创意生成价值}：差异化竞争优势
    \item[\faCheck] \textbf{思维模式方法}：发散+收敛+系统
    \item[\faCheck] \textbf{AI激发技术}：LLM+多模态+知识图谱
    \item[\faCheck] \textbf{工作流设计}：四阶段系统化流程
    \item[\faCheck] \textbf{质量控制}：三层筛选评估机制
\end{itemize}

\vspace{1em}
\textbf{能力提升目标：}
\begin{itemize}
    \item 创意思维训练
    \item AI工具熟练使用
    \item 选题策划能力
    \item 质量评估意识
\end{itemize}

\column{0.5\textwidth}
\begin{tikzpicture}[scale=0.7, mindmap, 
    level 1 concept/.append style={sibling angle=72},
    level 2 concept/.append style={sibling angle=30}]
    
    \node[concept, fill=CreativeOrange!50] {创意生成能力}
        child[concept color=red] {
            node[concept] {思维方法}
            child { node[concept] {头脑风暴} }
            child { node[concept] {SCAMPER} }
        }
        child[concept color=blue] {
            node[concept] {AI技术}
            child { node[concept] {LLM生成} }
            child { node[concept] {多模态} }
        }
        child[concept color=green] {
            node[concept] {工作流程}
            child { node[concept] {系统化} }
            child { node[concept] {标准化} }
        }
        child[concept color=purple] {
            node[concept] {质量控制}
            child { node[concept] {评估标准} }
            child { node[concept] {持续优化} }
        }
        child[concept color=orange] {
            node[concept] {实践应用}
            child { node[concept] {选题策划} }
            child { node[concept] {效果评估} }
        };
\end{tikzpicture}
\end{columns}

\vspace{1em}
\begin{exampleblock}{\faRocket 下周预告：第10周 - 创意生成辅助写作}
\textbf{学习重点：}文章框架搭建、段落生成技巧、风格统一控制、协作优化流程
\end{exampleblock}

\end{frame}

\begin{frame}
\frametitle{课后综合实践项目}

\textbf{项目主题：}为某品牌设计完整的内容营销创意方案

\vspace{0.5em}

\begin{columns}[T]
\column{0.5\textwidth}
\textbf{项目要求：}
\begin{enumerate}
    \item \textbf{品牌选择}（10分）
        \begin{itemize}
            \item 选择感兴趣的真实品牌
            \item 分析品牌现状和挑战
        \end{itemize}
    \item \textbf{创意策划}（40分）
        \begin{itemize}
            \item 使用AI工具辅助生成创意
            \item 应用至少3种思维方法
            \item 形成5个不同的创意方向
        \end{itemize}
    \item \textbf{方案评估}（30分）
        \begin{itemize}
            \item 建立评估标准体系
            \item 对比分析各方案优劣
            \item 选择最优方案并说明理由
        \end{itemize}
    \item \textbf{实施规划}（20分）
        \begin{itemize}
            \item 制定详细执行计划
            \item 预算和资源配置
            \item 风险控制措施
        \end{itemize}
\end{enumerate}

\column{0.5\textwidth}
\textbf{提交内容：}
\begin{itemize}
    \item[\faFile] 完整策划方案（3000-5000字）
    \item[\faImage] 创意展示PPT（15-20页）
    \item[\faCode] AI工具使用记录和反思
    \item[\faChartBar] 预期效果评估报告
\end{itemize}

\vspace{1em}
\textbf{评分标准：}
\begin{itemize}
    \item \textbf{创意新颖性}（30\%）
    \item \textbf{可执行性}（25\%）
    \item \textbf{商业价值}（25\%）
    \item \textbf{AI工具应用}（20\%）
\end{itemize}

\vspace{1em}
\begin{alertblock}{\faCalendar 时间安排}
\begin{itemize}
    \item 项目启动：课后即可开始
    \item 中期检查：下周三课堂讨论
    \item 最终提交：下周课前
    \item 成果展示：下周课堂分享（5分钟/组）
\end{itemize}
\end{alertblock}
\end{columns}

\end{frame}

\end{document}