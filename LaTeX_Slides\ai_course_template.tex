% AI-Driven Media Content Creation Course Template
% Base Beamer template for all course slides

\documentclass[aspectratio=169,xcolor=dvipsnames]{beamer}

% Package imports
\usepackage{xeCJK}
\usepackage{fontspec}
\usepackage{tikz}
\usepackage{listings}
\usepackage{booktabs}
\usepackage{graphicx}
\usepackage{hyperref}
\usepackage{enumitem}
\usepackage{array}
\usepackage{colortbl}
\usepackage{multirow}
\usepackage{textcomp}

% TikZ libraries
\usetikzlibrary{shapes,arrows,positioning,backgrounds,fit,decorations.pathreplacing}

% Font configuration
\setCJKmainfont{SimSun}
\setCJKsansfont{SimHei}
\setmainfont{Arial}
\setsansfont{Arial}

% Color definitions
\definecolor{AIBlue}{RGB}{41,128,185}
\definecolor{AILightBlue}{RGB}{52,152,219}
\definecolor{AIDarkBlue}{RGB}{31,97,141}
\definecolor{AIGreen}{RGB}{39,174,96}
\definecolor{AIGray}{RGB}{127,140,141}
\definecolor{AILightGray}{RGB}{236,240,241}
\definecolor{AIWhite}{RGB}{255,255,255}
\definecolor{AIRed}{RGB}{231,76,60}
\definecolor{AIOrange}{RGB}{230,126,34}

% Theme configuration
\usetheme{default}
\usecolortheme{default}

% Remove navigation symbols
\setbeamertemplate{navigation symbols}{}

% Custom color theme
\setbeamercolor{structure}{fg=AIBlue}
\setbeamercolor{palette primary}{bg=AIBlue,fg=white}
\setbeamercolor{palette secondary}{bg=AILightBlue,fg=white}
\setbeamercolor{palette tertiary}{bg=AIDarkBlue,fg=white}
\setbeamercolor{palette quaternary}{bg=AIGray,fg=white}

% Title page colors
\setbeamercolor{title}{fg=AIBlue}
\setbeamercolor{subtitle}{fg=AIGray}
\setbeamercolor{author}{fg=AIGray}
\setbeamercolor{institute}{fg=AIGray}
\setbeamercolor{date}{fg=AIGray}

% Frame title colors
\setbeamercolor{frametitle}{bg=AIBlue,fg=white}
\setbeamercolor{framesubtitle}{bg=AILightBlue,fg=white}

% Item colors
\setbeamercolor{item}{fg=AIBlue}
\setbeamercolor{subitem}{fg=AILightBlue}
\setbeamercolor{subsubitem}{fg=AIGreen}

% Block colors
\setbeamercolor{block title}{bg=AIBlue,fg=white}
\setbeamercolor{block body}{bg=AILightGray,fg=black}
\setbeamercolor{block title alerted}{bg=AIRed,fg=white}
\setbeamercolor{block body alerted}{bg=AILightGray,fg=black}
\setbeamercolor{block title example}{bg=AIGreen,fg=white}
\setbeamercolor{block body example}{bg=AILightGray,fg=black}

% Custom frame title template with gradient background
\setbeamertemplate{frametitle}{
    \begin{beamercolorbox}[wd=\paperwidth,ht=1.2cm,dp=0.3cm]{frametitle}
        \begin{tikzpicture}[remember picture,overlay]
            \fill[AIBlue] (current page.north west) rectangle ([yshift=-1.2cm]current page.north east);
            \fill[AILightBlue] ([yshift=-1.2cm]current page.north west) rectangle ([yshift=-1.5cm]current page.north east);
        \end{tikzpicture}
        \vbox to 1.2cm{
            \vfil
            \leftskip=0.5cm
            \insertframetitle\par
            \ifx\insertframesubtitle\@empty\else
                {\small\insertframesubtitle\par}
            \fi
            \vfil
        }
    \end{beamercolorbox}
}

% Custom title page template
\setbeamertemplate{title page}{
    \begin{tikzpicture}[remember picture,overlay]
        % Background gradient
        \fill[left color=AIBlue,right color=AILightBlue,opacity=0.1] 
              (current page.south west) rectangle (current page.north east);
        
        % Top decoration
        \fill[AIBlue] (current page.north west) rectangle ([yshift=-0.5cm]current page.north east);
        \fill[AILightBlue] ([yshift=-0.5cm]current page.north west) rectangle ([yshift=-0.7cm]current page.north east);
        
        % Bottom decoration
        \fill[AILightBlue] ([yshift=0.7cm]current page.south west) rectangle (current page.south east);
        \fill[AIBlue] (current page.south west) rectangle ([yshift=0.5cm]current page.south east);
    \end{tikzpicture}
    
    \vbox{}
    \vfill
    \begingroup
        \centering
        \begin{beamercolorbox}[sep=8pt,center]{title}
            \usebeamerfont{title}\inserttitle\par%
            \ifx\insertsubtitle\@empty\else%
                \vskip0.25em%
                {\usebeamerfont{subtitle}\usebeamercolor[fg]{subtitle}\insertsubtitle\par}%
            \fi%     
        \end{beamercolorbox}%
        \vskip1em\par
        \begin{beamercolorbox}[sep=8pt,center]{author}
            \usebeamerfont{author}\insertauthor
        \end{beamercolorbox}
        \begin{beamercolorbox}[sep=8pt,center]{institute}
            \usebeamerfont{institute}\insertinstitute
        \end{beamercolorbox}
        \begin{beamercolorbox}[sep=8pt,center]{date}
            \usebeamerfont{date}\insertdate
        \end{beamercolorbox}\vskip0.5em
    \endgroup
    \vfill
}

% Bullet point styling
\setbeamertemplate{itemize item}{\textcolor{AIBlue}{$\bullet$}}
\setbeamertemplate{itemize subitem}{\textcolor{AILightBlue}{$\circ$}}
\setbeamertemplate{itemize subsubitem}{\textcolor{AIGreen}{$\triangleright$}}

% Enumeration styling
\setbeamertemplate{enumerate item}{\textcolor{AIBlue}{\arabic{enumi}.}}
\setbeamertemplate{enumerate subitem}{\textcolor{AILightBlue}{\arabic{enumii}.}}

% Table styling
\newcommand{\tableheadcolor}{\rowcolor{AILightBlue}}
\newcommand{\tablealternatecolor}{\rowcolor{AILightGray}}

% Code listing style
\lstdefinestyle{AICodeStyle}{
    backgroundcolor=\color{AILightGray},
    commentstyle=\color{AIGray},
    keywordstyle=\color{AIBlue},
    numberstyle=\tiny\color{AIGray},
    stringstyle=\color{AIGreen},
    basicstyle=\ttfamily\footnotesize,
    breakatwhitespace=false,
    breaklines=true,
    captionpos=b,
    keepspaces=true,
    numbers=left,
    numbersep=5pt,
    showspaces=false,
    showstringspaces=false,
    showtabs=false,
    tabsize=2,
    frame=single,
    rulecolor=\color{AIBlue}
}
\lstset{style=AICodeStyle}

% Custom commands for consistent formatting
\newcommand{\highlight}[1]{\textcolor{AIBlue}{\textbf{#1}}}
\newcommand{\concept}[1]{\textcolor{AIGreen}{\textbf{#1}}}
\newcommand{\warning}[1]{\textcolor{AIRed}{\textbf{#1}}}
\newcommand{\success}[1]{\textcolor{AIGreen}{\textbf{#1}}}

% Emoji support (using text alternatives)
\newcommand{\emoji}[1]{
    \ifx#1🎯\textcolor{AIBlue}{$\bullet$}\fi%
    \ifx#1📚\textcolor{AIGreen}{$\blacksquare$}\fi%
    \ifx#1🔧\textcolor{AIOrange}{$\diamond$}\fi%
    \ifx#1💡\textcolor{AIOrange}{$\star$}\fi%
    \ifx#1🌍\textcolor{AIGreen}{$\circ$}\fi%
    \ifx#1📊\textcolor{AIBlue}{$\square$}\fi%
    % Add more emoji mappings as needed
}

% Section page template
\newcommand{\sectionpage}[1]{
    \begin{frame}[plain]
        \begin{tikzpicture}[remember picture,overlay]
            \fill[AIBlue] (current page.south west) rectangle (current page.north east);
            \node[white,font=\Huge\bfseries] at (current page.center) {#1};
        \end{tikzpicture}
    \end{frame}
}

% Timeline command for AI history
\newcommand{\timeline}[5]{
    \begin{tikzpicture}[scale=0.8,transform shape]
        \draw[thick,AIBlue] (0,0) -- (12,0);
        \foreach \x/\year in {0/#1,3/#2,6/#3,9/#4,12/#5}
        {
            \draw[thick,AIBlue] (\x,-0.2) -- (\x,0.2);
            \node[below,font=\small] at (\x,-0.5) {\year};
        }
    \end{tikzpicture}
}

% Footer with page numbers
\setbeamertemplate{footline}{%
    \leavevmode%
    \hbox{%
        \begin{beamercolorbox}[wd=.8\paperwidth,ht=2.25ex,dp=1ex,left]{title in head/foot}%
            \usebeamerfont{title in head/foot}\hspace{2em}AI驱动的传媒内容制作
        \end{beamercolorbox}%
        \begin{beamercolorbox}[wd=.2\paperwidth,ht=2.25ex,dp=1ex,right]{date in head/foot}%
            \usebeamerfont{date in head/foot}\insertframenumber{} / \inserttotalframenumber\hspace{2em}
        \end{beamercolorbox}%
    }%
    \vskip0pt%
}

% Course information
\title{AI驱动的传媒内容制作}
\subtitle{AI-Driven Media Content Creation}
\author{课程教师}
\institute{长江新闻与传播学院}
\date{\today}