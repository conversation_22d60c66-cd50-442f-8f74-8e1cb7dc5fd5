\input{../main_template.tex}

% Week 1 specific information
\title{AI驱动传媒内容制作}
\subtitle{第1周：课程导论与AI基础}
\author{授课教师}
\institute{汕头大学 长江新闻与传播学院}
\date{\today}

\begin{document}

% Title slide
\begin{frame}
\titlepage
\end{frame}

% Course information slide
\begin{frame}
\frametitle{课程信息}
\begin{block}{课程基本信息}
\begin{itemize}
    \item \textbf{课程代码：}JOU2155A
    \item \textbf{学分：}2学分
    \item \textbf{课时：}32课时（16周）
    \item \textbf{开课单位：}长江新闻与传播学院
\end{itemize}
\end{block}
\end{frame}

% Course objectives and learning outcomes
\begin{frame}
\frametitle{课程目标与预期学习成果}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{课程目标}
    \begin{itemize}
        \item[\faTarget] 掌握AI大模型在传媒领域的应用方法
        \item[\faTarget] 提升工作效率与内容质量
        \item[\faTarget] 培养AI时代的传媒人才
    \end{itemize}
\end{block}
\end{column}
\begin{column}{0.5\textwidth}
\begin{block}{预期学习成果}
    \textbf{知识层面：}
    \begin{itemize}
        \item 理解AI发展历程和LLM基本原理
        \item 掌握提示词工程核心技巧
        \item 熟悉主流AI平台和工具
    \end{itemize}
    
    \textbf{技能层面：}
    \begin{itemize}
        \item 信息获取与处理能力
        \item AI辅助内容创作技巧
        \item 复杂任务解决方案设计
    \end{itemize}
\end{block}
\end{column}
\end{columns}
\end{frame}

% Course arrangement and assessment
\begin{frame}
\frametitle{课程安排与考核方式}
\begin{columns}
\begin{column}{0.6\textwidth}
\begin{block}{教学安排}
    \begin{itemize}
        \item[\faCalendar] \textbf{16周课程：}每周2课时，共32课时
        \item[\faBook] \textbf{理论+实践：}理论讲解 + 实际操作 + 案例分析
        \item[\faRefresh] \textbf{ASK教学法：}Attitude + Skill + Knowledge
    \end{itemize}
\end{block}

\begin{block}{课程阶段}
    \begin{enumerate}
        \item \textbf{基础认知阶段}（第1-4周）：AI基础、提示词工程
        \item \textbf{技能应用阶段}（第5-10周）：信息获取、内容创作
        \item \textbf{进阶提升阶段}（第11-14周）：高级技巧、工具平台
        \item \textbf{综合实践阶段}（第15-16周）：项目实战、成果展示
    \end{enumerate}
\end{block}
\end{column}
\begin{column}{0.4\textwidth}
\begin{block}{考核方式}
    \begin{itemize}
        \item[\faChartBar] 日常考勤：10\%
        \item[\faChartBar] 课堂考查：10\%
        \item[\faChartBar] 日常作业：30\%
        \item[\faChartBar] 期末项目：50\%
    \end{itemize}
\end{block}
\end{column}
\end{columns}
\end{frame}

% Section: AI Development History
\section{AI发展简史}

% AI development timeline
\begin{frame}
\frametitle{人工智能发展简史}
\begin{center}
\begin{tikzpicture}[scale=0.8]
    \draw[thick,->] (0,0) -- (12,0);
    \foreach \x/\year in {1/1956,3/1980s,6/2000s,9/2010s,11/2020s}
        \draw (\x,0.1) -- (\x,-0.1) node[below] {\year};
    
    \node[above] at (1,0.2) {达特茅斯会议};
    \node[above] at (3,0.2) {专家系统时代};
    \node[above] at (6,0.2) {机器学习兴起};
    \node[above] at (9,0.2) {深度学习革命};
    \node[above] at (11,0.2) {大模型时代};
    
    \node[below] at (1,-0.5) {AI诞生};
    \node[below] at (3,-0.5) {知识驱动};
    \node[below] at (6,-0.5) {数据驱动};
    \node[below] at (9,-0.5) {神经网络};
    \node[below] at (11,-0.5) {Transformer};
\end{tikzpicture}
\end{center}

\begin{block}{关键节点}
\begin{itemize}
    \item[\faUniversity] \textbf{1956年：}达特茅斯会议，AI概念正式提出
    \item[\faBrain] \textbf{1980年代：}专家系统，知识工程兴起
    \item[\faChartLine] \textbf{2000年代：}机器学习，统计方法主导
    \item[\faLink] \textbf{2010年代：}深度学习，神经网络复兴
    \item[\faRocket] \textbf{2020年代：}大语言模型，通用人工智能
\end{itemize}
\end{block}
\end{frame}

% Dartmouth Conference
\begin{frame}
\frametitle{AI的诞生：达特茅斯会议}
\begin{columns}
\begin{column}{0.6\textwidth}
\begin{block}{历史背景}
    \begin{itemize}
        \item[\faCalendar] \textbf{时间：}1956年夏天
        \item[\faMapMarker] \textbf{地点：}美国达特茅斯学院
        \item[\faUsers] \textbf{参与者：}约翰·麦卡锡、马文·明斯基等10位科学家
        \item[\faTarget] \textbf{目标：}探讨机器模拟人类智能的可能性
    \end{itemize}
\end{block}

\begin{block}{重要意义}
    \begin{itemize}
        \item[\faTag] 首次提出"人工智能"（Artificial Intelligence）概念
        \item[\faTarget] 确立了AI的研究目标和方向
        \item[\faSeedling] 标志着AI作为独立学科的诞生
        \item[\faLightbulb] 提出了机器学习、自然语言处理等核心概念
    \end{itemize}
\end{block}
\end{column}
\begin{column}{0.4\textwidth}
\begin{alertblock}{经典名言}
"我们提议在1956年夏天，在新罕布什尔州汉诺威的达特茅斯学院进行一个为期2个月、由10个人参加的人工智能研究。"
\end{alertblock}
\end{column}
\end{columns}
\end{frame}

% Expert Systems Era
\begin{frame}
\frametitle{知识工程：专家系统时代}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{核心特征}
    \begin{itemize}
        \item[\faBrain] \textbf{知识驱动：}基于规则和专家知识
        \item[\faCog] \textbf{推理引擎：}逻辑推理和规则匹配
        \item[\faBook] \textbf{知识库：}领域专家经验的编码化
        \item[\faTarget] \textbf{专业领域：}医疗诊断、故障检测等
    \end{itemize}
\end{block}

\begin{block}{代表系统}
    \begin{itemize}
        \item[\faHospital] \textbf{MYCIN：}医疗诊断专家系统
        \item[\faWrench] \textbf{XCON：}计算机配置系统
        \item[\faGem] \textbf{PROSPECTOR：}地质勘探系统
    \end{itemize}
\end{block}
\end{column}
\begin{column}{0.5\textwidth}
\begin{alertblock}{局限性}
    \begin{itemize}
        \item[\faTimes] 知识获取困难（知识工程瓶颈）
        \item[\faTimes] 缺乏学习能力
        \item[\faTimes] 难以处理不确定性
        \item[\faTimes] 维护成本高昂
    \end{itemize}
\end{alertblock}

\begin{block}{对传媒的启示}
    \begin{itemize}
        \item[\faNewspaper] 早期的自动化新闻分类
        \item[\faChartBar] 简单的内容推荐系统
    \end{itemize}
\end{block}
\end{column}
\end{columns}
\end{frame}

% Machine Learning Era
\begin{frame}
\frametitle{数据驱动：机器学习革命}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{范式转变}
    \begin{itemize}
        \item[\faChartBar] \textbf{从规则到数据：}从手工编写规则到数据驱动学习
        \item[\faRefresh] \textbf{统计方法：}概率论、统计学成为核心工具
        \item[\faChartLine] \textbf{性能提升：}在多个任务上超越传统方法
    \end{itemize}
\end{block}

\begin{block}{核心算法}
    \begin{itemize}
        \item[\faTree] \textbf{决策树：}易于理解的分类方法
        \item[\faTarget] \textbf{支持向量机：}强大的分类和回归工具
        \item[\faSearch] \textbf{朴素贝叶斯：}基于概率的分类方法
        \item[\faDice] \textbf{随机森林：}集成学习的代表
    \end{itemize}
\end{block}
\end{column}
\begin{column}{0.5\textwidth}
\begin{block}{应用突破}
    \begin{itemize}
        \item[\faEnvelope] \textbf{垃圾邮件过滤：}贝叶斯分类器的成功应用
        \item[\faShoppingCart] \textbf{推荐系统：}协同过滤算法
        \item[\faSearch] \textbf{搜索引擎：}PageRank算法
        \item[\faChartBar] \textbf{数据挖掘：}从大数据中发现模式
    \end{itemize}
\end{block}

\begin{block}{传媒应用}
    \begin{itemize}
        \item[\faNewspaper] 自动新闻分类和标签
        \item[\faUsers] 用户行为分析和内容推荐
        \item[\faChartBar] 舆情监测和情感分析
    \end{itemize}
\end{block}
\end{column}
\end{columns}
\end{frame}

% Deep Learning Era
\begin{frame}
\frametitle{神经网络复兴：深度学习时代}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{技术突破}
    \begin{itemize}
        \item[\faBrain] \textbf{深层神经网络：}多层感知器的复兴
        \item[\faDesktop] \textbf{GPU加速：}并行计算能力的提升
        \item[\faChartBar] \textbf{大数据：}互联网时代的海量数据
        \item[\faRefresh] \textbf{反向传播：}高效的训练算法
    \end{itemize}
\end{block}

\begin{block}{里程碑事件}
    \begin{itemize}
        \item[\faTrophy] \textbf{2012年 ImageNet：}AlexNet在图像识别上的突破
        \item[\faGamepad] \textbf{2016年 AlphaGo：}在围棋上击败人类冠军
        \item[\faSpeaktoText] \textbf{语音识别：}接近人类水平的语音转文字
        \item[\faImage] \textbf{计算机视觉：}图像分类、目标检测的飞跃
    \end{itemize}
\end{block}
\end{column}
\begin{column}{0.5\textwidth}
\begin{block}{核心架构}
    \begin{itemize}
        \item[\faLink] \textbf{卷积神经网络（CNN）：}图像处理的利器
        \item[\faRefresh] \textbf{循环神经网络（RNN）：}序列数据的处理
        \item[\faTarget] \textbf{长短期记忆网络（LSTM）：}解决长序列问题
    \end{itemize}
\end{block}

\begin{block}{传媒革命}
    \begin{itemize}
        \item[\faCamera] 自动图像标注和内容识别
        \item[\faVideo] 视频内容分析和剪辑
        \item[\faEdit] 自动摘要和内容生成
        \item[\faSpeaktoText] 语音转文字和实时字幕
    \end{itemize}
\end{block}
\end{column}
\end{columns}
\end{frame}

% Transformer Architecture
\begin{frame}
\frametitle{注意力机制：Transformer的突破}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{历史背景}
    \begin{itemize}
        \item[\faCalendar] \textbf{2017年：}Google发布论文《Attention Is All You Need》
        \item[\faTarget] \textbf{目标：}解决RNN的序列处理限制
        \item[\faLightbulb] \textbf{核心创新：}完全基于注意力机制的架构
    \end{itemize}
\end{block}

\begin{block}{技术创新}
    \begin{itemize}
        \item[\faEye] \textbf{自注意力机制：}模型能够关注输入序列的不同部分
        \item[\faBolt] \textbf{并行计算：}摆脱了RNN的序列依赖，大幅提升训练效率
        \item[\faTarget] \textbf{位置编码：}解决序列位置信息的表示问题
        \item[\faRefresh] \textbf{多头注意力：}从多个角度理解输入信息
    \end{itemize}
\end{block}
\end{column}
\begin{column}{0.5\textwidth}
\begin{block}{架构优势}
    \begin{itemize}
        \item[\faRocket] \textbf{训练效率：}可以并行处理，训练速度大幅提升
        \item[\faTarget] \textbf{长距离依赖：}更好地捕捉长序列中的关系
        \item[\faWrench] \textbf{可解释性：}注意力权重提供了模型决策的可视化
        \item[\faGlobe] \textbf{通用性：}适用于多种NLP任务
    \end{itemize}
\end{block}

\begin{alertblock}{影响深远}
    \begin{itemize}
        \item[\faBuilding] 成为现代大语言模型的基础架构
        \item[\faChartLine] 推动了预训练模型的发展
        \item[\faGlobe] 影响了整个AI领域的发展方向
    \end{itemize}
\end{alertblock}
\end{column}
\end{columns}
\end{frame}

% GPT Evolution
\begin{frame}
\frametitle{GPT家族：从GPT-1到GPT-4}
\begin{center}
\begin{tikzpicture}[scale=0.8]
    \draw[thick,->] (0,0) -- (12,0);
    \foreach \x/\model in {1/GPT-1,4/GPT-2,7/GPT-3,10/GPT-4}
        \draw (\x,0.1) -- (\x,-0.1) node[below] {\model};
    
    \node[above] at (1,0.3) {2018};
    \node[above] at (4,0.3) {2019};
    \node[above] at (7,0.3) {2020};
    \node[above] at (10,0.3) {2023};
    
    \node[below] at (1,-0.7) {1.17亿参数};
    \node[below] at (4,-0.7) {15亿参数};
    \node[below] at (7,-0.7) {1750亿参数};
    \node[below] at (10,-0.7) {未公开参数量};
\end{tikzpicture}
\end{center}

\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{GPT-1（2018年）}
    \begin{itemize}
        \item[\faChartBar] \textbf{参数量：}1.17亿
        \item[\faTarget] \textbf{创新：}首次展示了预训练+微调的范式
        \item[\faBook] \textbf{数据：}BookCorpus数据集
        \item[\faLightbulb] \textbf{意义：}证明了无监督预训练的有效性
    \end{itemize}
\end{block}

\begin{block}{GPT-2（2019年）}
    \begin{itemize}
        \item[\faChartBar] \textbf{参数量：}15亿
        \item[\faFire] \textbf{争议：}因"过于危险"而延迟发布
        \item[\faEdit] \textbf{能力：}展现了强大的文本生成能力
        \item[\faGlobe] \textbf{影响：}引发了AI安全和伦理的讨论
    \end{itemize}
\end{block}
\end{column}
\begin{column}{0.5\textwidth}
\begin{block}{GPT-3（2020年）}
    \begin{itemize}
        \item[\faChartBar] \textbf{参数量：}1750亿
        \item[\faRocket] \textbf{突破：}展现了惊人的少样本学习能力
        \item[\faBriefcase] \textbf{商业化：}OpenAI API的推出
        \item[\faGlobe] \textbf{影响：}引发了大模型竞赛
    \end{itemize}
\end{block}

\begin{block}{GPT-4（2023年）}
    \begin{itemize}
        \item[\faRefresh] \textbf{多模态：}支持文本和图像输入
        \item[\faTarget] \textbf{性能：}在多项基准测试中接近人类水平
        \item[\faLightbulb] \textbf{应用：}ChatGPT Plus、Microsoft Copilot等
    \end{itemize}
\end{block}
\end{column}
\end{columns}
\end{frame}

% Large Language Model Era
\begin{frame}
\frametitle{大模型时代：通用人工智能的曙光}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{技术特征}
    \begin{itemize}
        \item[\faChartLine] \textbf{规模效应：}参数量呈指数级增长
        \item[\faTarget] \textbf{涌现能力：}规模达到临界点后出现的新能力
        \item[\faRefresh] \textbf{多模态融合：}文本、图像、音频的统一处理
        \item[\faGlobe] \textbf{通用性：}一个模型处理多种任务
    \end{itemize}
\end{block}

\begin{block}{主要玩家}
    \begin{itemize}
        \item[\faFlag] \textbf{OpenAI：}GPT系列、ChatGPT、GPT-4
        \item[\faFlag] \textbf{Google：}LaMDA、PaLM、Gemini
        \item[\faFlag] \textbf{Anthropic：}Claude系列
        \item[\faFlag] \textbf{百度：}文心一言
        \item[\faFlag] \textbf{阿里：}通义千问
        \item[\faFlag] \textbf{字节跳动：}豆包
    \end{itemize}
\end{block}
\end{column}
\begin{column}{0.5\textwidth}
\begin{block}{能力突破}
    \begin{itemize}
        \item[\faComments] \textbf{对话能力：}接近人类的自然对话
        \item[\faEdit] \textbf{内容创作：}高质量的文本生成
        \item[\faCalculator] \textbf{推理能力：}复杂问题的逻辑推理
        \item[\faWrench] \textbf{代码生成：}编程任务的自动化
        \item[\faPalette] \textbf{创意设计：}艺术创作和设计辅助
    \end{itemize}
\end{block}

\begin{alertblock}{社会影响}
    \begin{itemize}
        \item[\faBriefcase] 改变工作方式和职业结构
        \item[\faBook] 革新教育和学习模式
        \item[\faMasks] 推动创意产业发展
        \item[\faGavel] 引发伦理和监管讨论
    \end{itemize}
\end{alertblock}
\end{column}
\end{columns}
\end{frame}

% Section: Machine Learning Basics
\section{机器学习基础}

% What is Machine Learning
\begin{frame}
\frametitle{机器学习：让机器从数据中学习}
\begin{block}{定义}
机器学习是一种人工智能方法，使计算机系统能够通过经验自动改进性能，而无需明确编程。
\end{block}

\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{核心思想}
    \begin{itemize}
        \item[\faChartBar] \textbf{数据驱动：}从数据中发现模式和规律
        \item[\faRefresh] \textbf{自动学习：}算法自动调整参数
        \item[\faTarget] \textbf{泛化能力：}在新数据上表现良好
        \item[\faChartLine] \textbf{持续改进：}随着数据增加而提升性能
    \end{itemize}
\end{block}

\begin{block}{生活中的例子}
    \begin{itemize}
        \item[\faEnvelope] 邮件垃圾过滤
        \item[\faShoppingCart] 商品推荐系统
        \item[\faMap] 导航路径规划
        \item[\faMobile] 语音助手识别
    \end{itemize}
\end{block}
\end{column}
\begin{column}{0.5\textwidth}
\begin{block}{与传统编程的区别}
\begin{center}
\begin{tabular}{|c|c|}
\hline
\textbf{传统编程} & \textbf{机器学习} \\
\hline
人工编写规则 & 从数据中学习规则 \\
\hline
逻辑驱动 & 数据驱动 \\
\hline
确定性输出 & 概率性输出 \\
\hline
难以处理复杂模式 & 擅长发现复杂模式 \\
\hline
\end{tabular}
\end{center}
\end{block}
\end{column}
\end{columns}
\end{frame}

% Types of Machine Learning
\begin{frame}
\frametitle{机器学习的分类：监督、无监督、强化学习}
\begin{columns}
\begin{column}{0.33\textwidth}
\begin{block}{监督学习}
\textbf{Supervised Learning}
\begin{itemize}
    \item[\faBook] \textbf{特点：}有标注的训练数据
    \item[\faTarget] \textbf{目标：}学习输入到输出的映射关系
    \item[\faChartBar] \textbf{应用：}分类、回归问题
\end{itemize}

\textbf{例子：}
\begin{itemize}
    \item 邮件分类
    \item 房价预测
    \item 图像识别
\end{itemize}
\end{block}
\end{column}
\begin{column}{0.33\textwidth}
\begin{block}{无监督学习}
\textbf{Unsupervised Learning}
\begin{itemize}
    \item[\faSearch] \textbf{特点：}没有标注的数据
    \item[\faTarget] \textbf{目标：}发现数据中的隐藏模式
    \item[\faChartBar] \textbf{应用：}聚类、降维、异常检测
\end{itemize}

\textbf{例子：}
\begin{itemize}
    \item 用户群体分析
    \item 主题发现
    \item 异常检测
\end{itemize}
\end{block}
\end{column}
\begin{column}{0.33\textwidth}
\begin{block}{强化学习}
\textbf{Reinforcement Learning}
\begin{itemize}
    \item[\faGamepad] \textbf{特点：}通过与环境交互学习
    \item[\faTarget] \textbf{目标：}最大化累积奖励
    \item[\faChartBar] \textbf{应用：}游戏、机器人控制、推荐系统
\end{itemize}

\textbf{例子：}
\begin{itemize}
    \item AlphaGo
    \item 自动驾驶
    \item 个性化推荐
\end{itemize}
\end{block}
\end{column}
\end{columns}
\end{frame}

% Deep Learning Introduction
\begin{frame}
\frametitle{深度学习：模拟大脑的神经网络}
\begin{columns}
\begin{column}{0.6\textwidth}
\begin{block}{什么是神经网络？}
    \begin{itemize}
        \item[\faBrain] \textbf{灵感来源：}模拟人脑神经元的连接方式
        \item[\faLink] \textbf{基本单元：}人工神经元（感知器）
        \item[\faGlobe] \textbf{网络结构：}多层神经元的连接
        \item[\faBolt] \textbf{信息传递：}通过权重和激活函数处理信息
    \end{itemize}
\end{block}

\begin{block}{深度学习的特点}
    \begin{itemize}
        \item[\faChartBar] \textbf{多层结构：}通常包含多个隐藏层
        \item[\faRefresh] \textbf{自动特征提取：}无需手工设计特征
        \item[\faChartLine] \textbf{端到端学习：}从原始数据直接到最终结果
        \item[\faStrong] \textbf{强大表达能力：}能够学习复杂的非线性关系
    \end{itemize}
\end{block}
\end{column}
\begin{column}{0.4\textwidth}
\begin{block}{神经网络结构图示}
\begin{center}
\begin{tikzpicture}[scale=0.6]
    % Input layer
    \foreach \y in {1,2,3}
        \node[circle,draw,fill=light] (I\y) at (0,\y) {$x_\y$};
    
    % Hidden layer
    \foreach \y in {1,2,3}
        \node[circle,draw,fill=accent] (H\y) at (3,\y) {$h_\y$};
    
    % Output layer
    \foreach \y in {1,2,3}
        \node[circle,draw,fill=secondary] (O\y) at (6,\y) {$y_\y$};
    
    % Connections
    \foreach \i in {1,2,3}
        \foreach \j in {1,2,3}
            \draw[arrow] (I\i) -- (H\j);
    
    \foreach \i in {1,2,3}
        \foreach \j in {1,2,3}
            \draw[arrow] (H\i) -- (O\j);
    
    \node at (0,0) {输入层};
    \node at (3,0) {隐藏层};
    \node at (6,0) {输出层};
\end{tikzpicture}
\end{center}
\end{block}

\begin{alertblock}{深度学习的优势}
    \begin{itemize}
        \item[\faTarget] 性能突出
        \item[\faWrench] 自动化程度高
        \item[\faGlobe] 通用性强
        \item[\faChartLine] 可扩展性
    \end{itemize}
\end{alertblock}
\end{column}
\end{columns}
\end{frame}

% Deep Learning in Media
\begin{frame}
\frametitle{深度学习革新传媒行业}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{图像处理应用}
    \begin{itemize}
        \item[\faCamera] \textbf{自动标注：}为图片自动添加描述和标签
        \item[\faSearch] \textbf{内容识别：}识别图片中的人物、物体、场景
        \item[\faPalette] \textbf{图像生成：}AI绘画、图像修复和增强
        \item[\faChartBar] \textbf{视觉分析：}从图像中提取数据和洞察
    \end{itemize}
\end{block}

\begin{block}{视频处理应用}
    \begin{itemize}
        \item[\faCut] \textbf{智能剪辑：}自动识别精彩片段
        \item[\faEdit] \textbf{字幕生成：}自动语音识别和字幕制作
        \item[\faTarget] \textbf{内容分析：}视频内容的自动分类和标签
        \item[\faSearch] \textbf{人脸识别：}自动识别视频中的人物
    \end{itemize}
\end{block}
\end{column}
\begin{column}{0.5\textwidth}
\begin{block}{文本处理应用}
    \begin{itemize}
        \item[\faNewspaper] \textbf{自动摘要：}长文本的智能摘要生成
        \item[\faTag] \textbf{内容分类：}新闻文章的自动分类
        \item[\faHeart] \textbf{情感分析：}分析文本的情感倾向
        \item[\faRefresh] \textbf{语言翻译：}多语言内容的自动翻译
    \end{itemize}
\end{block}

\begin{block}{音频处理应用}
    \begin{itemize}
        \item[\faSpeaktoText] \textbf{语音转文字：}播客、采访的自动转录
        \item[\faMusic] \textbf{音乐分析：}音乐风格和情感分析
        \item[\faVolumeUp] \textbf{音频增强：}噪音消除和音质提升
        \item[\faMicrophone] \textbf{语音合成：}AI主播和配音
    \end{itemize}
\end{block}
\end{column}
\end{columns}

\begin{alertblock}{实际案例}
    \begin{itemize}
        \item[\faTv] \textbf{新华社AI主播：}虚拟主播播报新闻
        \item[\faMobile] \textbf{抖音推荐算法：}个性化内容推荐
        \item[\faNewspaper] \textbf{今日头条：}智能内容分发
        \item[\faFilm] \textbf{Netflix：}个性化内容推荐
    \end{itemize}
\end{alertblock}
\end{frame}

% Data-Driven Learning Paradigm
\begin{frame}
\frametitle{数据驱动：机器学习的核心范式}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{传统方法 vs 数据驱动方法}
\begin{center}
\begin{tabular}{|c|c|}
\hline
\textbf{传统方法} & \textbf{数据驱动方法} \\
\hline
专家知识 & 数据模式 \\
\hline
手工规则 & 自动学习 \\
\hline
逻辑推理 & 统计推断 \\
\hline
确定性 & 概率性 \\
\hline
难以扩展 & 易于扩展 \\
\hline
\end{tabular}
\end{center}
\end{block}

\begin{block}{数据的重要性}
    \begin{itemize}
        \item[\faChartBar] \textbf{数据质量决定模型性能：}垃圾进，垃圾出
        \item[\faChartLine] \textbf{数据量影响学习效果：}更多数据通常带来更好性能
        \item[\faTarget] \textbf{数据多样性提升泛化能力：}覆盖更多场景
        \item[\faRefresh] \textbf{数据更新保持模型时效性：}适应变化的环境
    \end{itemize}
\end{block}
\end{column}
\begin{column}{0.5\textwidth}
\begin{block}{数据驱动学习的流程}
\begin{center}
\begin{tikzpicture}[scale=0.5]
    \node[box] (data) at (0,0) {原始数据};
    \node[box] (preprocess) at (3,0) {数据预处理};
    \node[box] (feature) at (6,0) {特征提取};
    \node[box] (train) at (0,-2) {模型训练};
    \node[box] (eval) at (3,-2) {模型评估};
    \node[box] (deploy) at (6,-2) {应用部署};
    
    \draw[arrow] (data) -- (preprocess);
    \draw[arrow] (preprocess) -- (feature);
    \draw[arrow] (feature) -- (train);
    \draw[arrow] (train) -- (eval);
    \draw[arrow] (eval) -- (deploy);
\end{tikzpicture}
\end{center}
\end{block}

\begin{block}{在传媒中的应用}
    \begin{itemize}
        \item[\faNewspaper] \textbf{内容分析：}从大量新闻中发现趋势
        \item[\faUsers] \textbf{用户画像：}基于行为数据理解用户
        \item[\faChartBar] \textbf{效果评估：}数据驱动的内容效果分析
        \item[\faTarget] \textbf{精准推送：}基于数据的个性化推荐
    \end{itemize}
\end{block}

\begin{alertblock}{挑战与机遇}
    \begin{itemize}
        \item[\faExclamationTriangle] \textbf{数据隐私：}用户数据保护的重要性
        \item[\faTarget] \textbf{数据偏见：}避免算法歧视
        \item[\faChartBar] \textbf{数据质量：}确保数据的准确性和完整性
        \item[\faRefresh] \textbf{持续学习：}模型的持续更新和优化
    \end{itemize}
\end{alertblock}
\end{column}
\end{columns}
\end{frame}

% ML Evolution in Media
\begin{frame}
\frametitle{传媒行业的AI演进之路}
\begin{block}{第一阶段：自动化处理（2000-2010年）}
    \begin{itemize}
        \item[\faWrench] \textbf{基础自动化：}简单的文本处理和分类
        \item[\faChartBar] \textbf{数据统计：}基础的数据分析和报表生成
        \item[\faTag] \textbf{内容标签：}简单的关键词提取和标签
        \item[\faChartLine] \textbf{效果有限：}主要用于提升效率
    \end{itemize}
\end{block}

\begin{block}{第二阶段：智能分析（2010-2020年）}
    \begin{itemize}
        \item[\faBrain] \textbf{机器学习应用：}更复杂的模式识别
        \item[\faUsers] \textbf{用户分析：}基于行为的用户画像
        \item[\faMobile] \textbf{个性化推荐：}算法驱动的内容分发
        \item[\faHeart] \textbf{情感分析：}理解用户情感和态度
    \end{itemize}
\end{block}

\begin{block}{第三阶段：内容生成（2020年至今）}
    \begin{itemize}
        \item[\faRobot] \textbf{AI创作：}自动生成文本、图像、视频
        \item[\faComments] \textbf{智能对话：}AI客服和虚拟主播
        \item[\faPalette] \textbf{创意辅助：}AI辅助的创意设计
        \item[\faRefresh] \textbf{全流程覆盖：}从策划到发布的全链条AI应用
    \end{itemize}
\end{block}

\begin{alertblock}{未来展望：第四阶段（未来5-10年）}
    \begin{itemize}
        \item[\faBrain] \textbf{通用AI助手：}全能的传媒工作伙伴
        \item[\faGlobe] \textbf{多模态融合：}文本、图像、音频的统一处理
        \item[\faTarget] \textbf{超个性化：}基于深度理解的精准服务
        \item[\faHandshake] \textbf{人机协作：}AI与人类的深度协作
    \end{itemize}
\end{alertblock}
\end{frame>

% Section: LLM Overview
\section{LLM概述}

% What is LLM
\begin{frame}
\frametitle{大语言模型（LLM）：AI的新里程碑}
\begin{block}{定义}
大语言模型（Large Language Model, LLM）是基于深度学习的自然语言处理模型，通过在大规模文本数据上进行预训练，具备强大的语言理解和生成能力。
\end{block>

\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{核心特征}
    \begin{itemize}
        \item[\faChartBar] \textbf{规模庞大：}参数量通常在数十亿到数千亿级别
        \item[\faBook] \textbf{预训练：}在海量文本数据上进行无监督学习
        \item[\faTarget] \textbf{通用性：}一个模型可以处理多种NLP任务
        \item[\faLightbulb] \textbf{涌现能力：}规模达到临界点后出现的新能力
    \end{itemize}
\end{block}

\begin{block}{技术基础}
    \begin{itemize}
        \item[\faBuilding] \textbf{Transformer架构：}基于注意力机制的神经网络
        \item[\faRefresh] \textbf{自回归生成：}逐词预测下一个词
        \item[\faBook] \textbf{上下文学习：}利用上下文信息进行推理
        \item[\faGraduationCap] \textbf{迁移学习：}预训练模型的知识迁移
    \end{itemize}
\end{block}
\end{column}
\begin{column}{0.5\textwidth}
\begin{block}{与传统NLP模型的区别}
\begin{center}
\begin{tabular}{|c|c|}
\hline
\textbf{传统NLP模型} & \textbf{大语言模型} \\
\hline
任务特定 & 通用多任务 \\
\hline
小规模数据 & 海量数据 \\
\hline
有监督学习 & 自监督预训练 \\
\hline
规则+特征工程 & 端到端学习 \\
\hline
性能有限 & 接近人类水平 \\
\hline
\end{tabular}
\end{center>
\end{block}
\end{column>
\end{columns}
\end{frame}

% LLM Breakthrough Significance
\begin{frame}
\frametitle{LLM的革命性突破}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{技术突破}
    \begin{itemize}
        \item[\faTarget] \textbf{零样本学习：}无需训练即可完成新任务
        \item[\faBook] \textbf{少样本学习：}仅需少量示例即可快速适应
        \item[\faBrain] \textbf{上下文学习：}在对话中学习和适应
        \item[\faLightbulb] \textbf{涌现能力：}规模增大带来的质变
    \end{itemize}
\end{block}

\begin{block}{能力展现}
    \begin{itemize}
        \item[\faComments] \textbf{自然对话：}接近人类的对话能力
        \item[\faEdit] \textbf{文本生成：}高质量的内容创作
        \item[\faCalculator] \textbf{逻辑推理：}复杂问题的分析和解决
        \item[\faRefresh] \textbf{多任务处理：}一个模型胜任多种任务
        \item[\faGlobe] \textbf{多语言支持：}跨语言的理解和生成
    \end{itemize}
\end{block>
\end{column}
\begin{column}{0.5\textwidth}
\begin{block}{对AI领域的影响}
    \begin{itemize}
        \item[\faRocket] \textbf{范式转变：}从任务特定到通用智能
        \item[\faWrench] \textbf{开发效率：}大幅降低AI应用开发门槛
        \item[\faGlobe] \textbf{应用普及：}AI技术的大众化
        \item[\faBriefcase] \textbf{商业价值：}创造新的商业模式和机会
    \end{itemize}
\end{block}

\begin{block}{社会影响}
    \begin{itemize}
        \item[\faBook] \textbf{教育变革：}个性化学习和智能辅导
        \item[\faBriefcase] \textbf{工作方式：}AI助手成为工作伙伴
        \item[\faPalette] \textbf{创意产业：}AI辅助的内容创作
        \item[\faHospital] \textbf{专业服务：}智能化的专业咨询
    \end{itemize}
\end{block}

\begin{alertblock}{挑战与思考}
    \begin{itemize}
        \item[\faGavel] \textbf{伦理问题：}AI的责任和边界
        \item[\faLock] \textbf{安全风险：}模型的可控性和安全性
        \item[\faBriefcase] \textbf{就业影响：}对传统工作的冲击
        \item[\faGlobe] \textbf{数字鸿沟：}技术普及的公平性
    \end{itemize}
\end{alertblock}
\end{column>
\end{columns}
\end{frame}

% LLM Applications in Media
\begin{frame}
\frametitle{LLM重塑传媒全流程}
\begin{center}
\begin{tikzpicture}[scale=0.8]
    \node[box] (collect) at (0,2) {采集};
    \node[box] (edit) at (2.5,2) {编辑};
    \node[box] (produce) at (5,2) {制作};
    \node[box] (publish) at (7.5,2) {发布};
    \node[box] (evaluate) at (10,2) {评估};
    
    \node at (0,1) {信息搜集};
    \node at (2.5,1) {内容创作};
    \node at (5,1) {多媒体制作};
    \node at (7.5,1) {分发推广};
    \node at (10,1) {效果分析};
    
    \draw[arrow] (collect) -- (edit);
    \draw[arrow] (edit) -- (produce);
    \draw[arrow] (produce) -- (publish);
    \draw[arrow] (publish) -- (evaluate);
\end{tikzpicture}
\end{center}

\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{1. 采集环节（信息获取）}
    \begin{itemize}
        \item[\faSearch] \textbf{智能搜索：}快速搜集相关信息和背景资料
        \item[\faChartBar] \textbf{数据分析：}从大量数据中提取关键信息
        \item[\faSpeaktoText] \textbf{访谈辅助：}生成采访问题和后续追问
        \item[\faNewspaper] \textbf{线索发现：}从海量信息中发现新闻线索
        \item[\faCheck] \textbf{事实核查：}辅助验证信息的真实性
    \end{itemize}
\end{block>

\begin{block}{2. 编辑环节（内容创作）}
    \begin{itemize}
        \item[\faEdit] \textbf{写作辅助：}提供写作建议和内容框架
        \item[\faFileText] \textbf{自动摘要：}快速生成文章摘要和要点
        \item[\faRefresh] \textbf{内容改写：}不同风格和受众的内容适配
        \item[\faTag] \textbf{标题生成：}创作吸引人的标题和导语
        \item[\faBook] \textbf{多语言翻译：}跨语言内容的快速翻译
    \end{itemize}
\end{block>

\begin{block}{3. 制作环节（多媒体制作）}
    \begin{itemize}
        \item[\faFilm] \textbf{脚本创作：}视频和音频内容的脚本生成
        \item[\faImage] \textbf{图文配对：}为文章匹配合适的图片和说明
        \item[\faMicrophone] \textbf{配音生成：}AI语音合成和配音
        \item[\faCut] \textbf{剪辑辅助：}视频剪辑的智能建议
        \item[\faPalette] \textbf{视觉设计：}图表、海报等视觉元素生成
    \end{itemize}
\end{block>
\end{column}
\begin{column>{0.5\textwidth}
\begin{block}{4. 发布环节（分发推广）}
    \begin{itemize}
        \item[\faMobile] \textbf{多平台适配：}针对不同平台的内容优化
        \item[\faTarget] \textbf{个性化推荐：}基于用户兴趣的精准推送
        \item[\faClock] \textbf{发布时机：}最佳发布时间的智能建议
        \item[\faTag] \textbf{标签优化：}SEO和社交媒体标签生成
        \item[\faComments] \textbf{互动回复：}自动回复用户评论和私信
    \end{itemize}
\end{block>

\begin{block}{5. 评估环节（效果分析）}
    \begin{itemize}
        \item[\faChartBar] \textbf{数据分析：}传播效果的深度分析
        \item[\faHeart] \textbf{情感监测：}用户反馈的情感分析
        \item[\faChartLine] \textbf{趋势预测：}内容传播趋势的预测
        \item[\faTarget] \textbf{优化建议：}基于数据的改进建议
        \item[\faClipboard] \textbf{报告生成：}自动生成分析报告
    \end{itemize}
\end{block>
\end{column>
\end{columns}
\end{frame}

% LLM Core Advantages
\begin{frame}
\frametitle{LLM的独特优势}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{1. 强大的语言理解能力}
    \begin{itemize}
        \item[\faBrain] \textbf{深度理解：}理解文本的深层含义和隐含信息
        \item[\faGlobe] \textbf{上下文感知：}基于上下文进行准确理解
        \item[\faLightbulb] \textbf{推理能力：}进行逻辑推理和因果分析
        \item[\faTarget] \textbf{意图识别：}准确识别用户的真实意图
    \end{itemize}
\end{block}

\begin{block}{2. 灵活的内容生成能力}
    \begin{itemize}
        \item[\faEdit] \textbf{多样化写作：}适应不同风格和体裁
        \item[\faPalette] \textbf{创意生成：}产生新颖的想法和创意
        \item[\faRefresh] \textbf{格式适配：}生成各种格式的内容
        \item[\faChartBar] \textbf{结构化输出：}按要求组织信息结构
    \end{itemize}
\end{block>

\begin{block}{3. 高效的学习适应能力}
    \begin{itemize}
        \item[\faRocket] \textbf{快速学习：}通过少量示例快速适应新任务
        \item[\faRefresh] \textbf{持续改进：}在交互中不断优化表现
        \item[\faTarget] \textbf{个性化：}根据用户偏好调整输出风格
        \item[\faGlobe] \textbf{跨领域应用：}在不同领域间迁移知识
    \end{itemize}
\end{block>
\end{column}
\begin{column}{0.5\textwidth}
\begin{block}{4. 便捷的交互方式}
    \begin{itemize}
        \item[\faComments] \textbf{自然语言交互：}用日常语言进行沟通
        \item[\faWrench] \textbf{低门槛使用：}无需编程技能即可使用
        \item[\faBolt] \textbf{即时响应：}快速获得结果和反馈
        \item[\faSliders] \textbf{灵活控制：}通过提示词精确控制输出
    \end{itemize}
\end{block>

\begin{block}{5. 成本效益优势}
    \begin{itemize}
        \item[\faDollarSign] \textbf{降低成本：}减少人力和时间投入
        \item[\faChartLine] \textbf{提升效率：}大幅提高工作效率
        \item[\faRefresh] \textbf{24/7可用：}全天候不间断服务
        \item[\faChartBar] \textbf{规模化应用：}轻松处理大量任务
    \end{itemize}
\end{block>
\end{column>
\end{columns}
\end{frame}

% LLM Limitations and Challenges
\begin{frame}
\frametitle{理性认识LLM的局限性}
\begin{columns}
\begin{column>{0.5\textwidth}
\begin{alertblock}{技术局限性}
    \begin{itemize}
        \item[\faCrystalBall] \textbf{幻觉问题：}可能生成看似合理但实际错误的信息
        \item[\faCalendar] \textbf{知识截止：}训练数据的时间限制
        \item[\faCalculator] \textbf{数学计算：}在复杂数学运算上的不足
        \item[\faSearch] \textbf{事实核查：}无法实时验证信息的准确性
        \item[\faSave] \textbf{记忆限制：}上下文长度的限制
    \end{itemize>
\end{alertblock}

\begin{alertblock}{应用挑战}
    \begin{itemize}
        \item[\faTarget] \textbf{一致性问题：}同样问题可能得到不同答案
        \item[\faWrench] \textbf{可控性：}难以精确控制输出结果
        \item[\faChartBar] \textbf{评估困难：}输出质量的客观评估
        \item[\faRefresh] \textbf{更新滞后：}模型更新的时间成本
    \end{itemize}
\end{alertblock}
\end{column}
\begin{column>{0.5\textwidth}
\begin{alertblock}{伦理与安全问题}
    \begin{itemize>
        \item[\faGavel] \textbf{偏见问题：}训练数据中的偏见可能被放大
        \item[\faLock] \textbf{隐私风险：}可能泄露训练数据中的敏感信息
        \item[\faBriefcase] \textbf{就业影响：}对传统工作岗位的冲击
        \item[\faGlobe] \textbf{信息茧房：}可能加剧信息分化
    \end{itemize>
\end{alertblock}

\begin{alertblock}{在传媒中的特殊挑战}
    \begin{itemize}
        \item[\faNewspaper] \textbf{新闻真实性：}确保生成内容的准确性
        \item[\faGavel] \textbf{媒体责任：}AI生成内容的责任归属
        \item[\faTarget] \textbf{受众信任：}维护媒体的公信力
        \item[\faChartBar] \textbf{质量控制：}建立有效的内容审核机制
    \end{itemize}
\end{alertblock}

\begin{block}{应对策略}
    \begin{itemize}
        \item[\faCheck] \textbf{人工审核：}建立人机结合的审核机制
        \item[\faSearch] \textbf{多源验证：}交叉验证信息的准确性
        \item[\faBook] \textbf{持续学习：}定期更新和优化模型
        \item[\faGavel] \textbf{伦理规范：}建立AI使用的伦理准则
    \end{itemize}
\end{block>
\end{column>
\end{columns}
\end{frame}

% Section: Media Application Prospects
\section{传媒应用展望}

% AI+Media Development Trends
\begin{frame}
\frametitle{AI+传媒：未来已来}
\begin{columns}
\begin{column>{0.5\textwidth}
\begin{block}{当前发展阶段}
    \begin{itemize}
        \item[\faWrench] \textbf{工具辅助阶段：}AI作为提升效率的工具
        \item[\faHandshake] \textbf{人机协作阶段：}AI与人类深度协作
        \item[\faRocket] \textbf{智能化转型阶段：}传媒行业的全面智能化
    \end{itemize}
\end{block>

\begin{block}{技术发展趋势}
    \begin{itemize}
        \item[\faGlobe] \textbf{多模态融合：}文本、图像、音频、视频的统一处理
        \item[\faBrain] \textbf{更强推理能力：}接近人类的逻辑推理和创造力
        \item[\faTarget] \textbf{个性化定制：}基于深度理解的超个性化服务
        \item[\faBolt] \textbf{实时处理：}更快的响应速度和处理能力
        \item[\faRefresh] \textbf{持续学习：}模型的在线学习和自我优化
    \end{itemize}
\end{block}
\end{column}
\begin{column>{0.5\textwidth}
\begin{block}{应用场景展望}
    \begin{itemize>
        \item[\faNewspaper] \textbf{智能新闻编辑室：}AI记者、编辑、主播的协同工作
        \item[\faFilm] \textbf{自动化内容生产：}从策划到制作的全流程自动化
        \item[\faUsers] \textbf{超个性化媒体：}为每个用户定制的专属内容
        \item[\faGlobe] \textbf{全球化传播：}实时多语言内容生产和分发
        \item[\faChartBar] \textbf{数据驱动决策：}基于AI分析的内容策略制定
    \end{itemize>
\end{block}

\begin{alertblock}{行业变革预期}
    \begin{itemize>
        \item[\faBriefcase] \textbf{新职业诞生：}AI训练师、提示词工程师等
        \item[\faRefresh] \textbf{工作流程重构：}传统工作流程的彻底改变
        \item[\faTarget] \textbf{商业模式创新：}基于AI的新商业模式
        \item[\faGlobe] \textbf{媒体形态演进：}新的媒体形态和传播方式
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns}
\end{frame}

% Opportunities and Challenges for Media Professionals
\begin{frame}
\frametitle{AI时代的传媒人：拥抱变化，创造未来}
\begin{columns}
\begin{column>{0.5\textwidth}
\begin{block>{新机遇}
    \begin{itemize>
        \item[\faRocket] \textbf{效率提升：}AI工具大幅提升工作效率
        \item[\faPalette] \textbf{创意增强：}AI辅助激发更多创意灵感
        \item[\faChartBar] \textbf{数据洞察：}基于AI的深度数据分析能力
        \item[\faGlobe] \textbf{全球视野：}跨语言、跨文化的内容创作
        \item[\faLightbulb] \textbf{新技能发展：}掌握AI工具的竞争优势
    \end{itemize>
\end{block>

\begin{alertblock>{面临挑战}
    \begin{itemize>
        \item[\faBook] \textbf{技能更新：}需要不断学习新技术和工具
        \item[\faRobot] \textbf{角色重新定义：}从执行者向策划者、监督者转变
        \item[\faGavel] \textbf{伦理责任：}AI时代的媒体责任和伦理考量
        \item[\faTarget] \textbf{质量控制：}确保AI生成内容的质量和准确性
        \item[\faBriefcase] \textbf{职业转型：}适应新的工作方式和要求
    \end{itemize>
\end{alertblock>
\end{column}
\begin{column>{0.5\textwidth}
\begin{block>{必备能力}
    \begin{itemize>
        \item[\faBrain] \textbf{AI素养：}理解AI技术的原理和应用
        \item[\faWrench] \textbf{工具使用：}熟练掌握各种AI工具
        \item[\faThink] \textbf{批判思维：}对AI输出进行批判性评估
        \item[\faPalette] \textbf{创意思维：}发挥人类独有的创造力
        \item[\faHandshake] \textbf{协作能力：}与AI系统有效协作
    \end{itemize>
\end{block>

\begin{block>{发展建议}
    \begin{itemize>
        \item[\faBook] \textbf{持续学习：}保持对新技术的敏感度和学习能力
        \item[\faMicroscope] \textbf{实践探索：}积极尝试和应用新的AI工具
        \item[\faGlobe] \textbf{跨界思维：}结合传媒专业知识和AI技术
        \item[\faHandshake] \textbf{社群参与：}加入AI+传媒的学习和交流社群
        \item[\faTarget] \textbf{专业深化：}在某个细分领域成为AI应用专家
    \end{itemize>
\end{block>
\end{column>
\end{columns}
\end{frame>

% Course Learning Guide
\begin{frame}
\frametitle{开启AI+传媒学习之旅}
\begin{columns}
\begin{column>{0.5\textwidth}
\begin{block>{学习目标设定}
    \textbf{短期目标（课程期间）：}
    \begin{itemize>
        \item 掌握AI基础概念和LLM原理
        \item 熟练使用主流AI平台和工具
        \item 具备AI辅助内容创作的基本能力
    \end{itemize>
    
    \textbf{中期目标（毕业前）：}
    \begin{itemize>
        \item 在某个传媒细分领域深度应用AI
        \item 建立个人的AI工具使用体系
        \item 培养AI伦理和责任意识
    \end{itemize>
    
    \textbf{长期目标（职业发展）：}
    \begin{itemize>
        \item 成为AI时代的复合型传媒人才
        \item 引领行业AI应用创新
        \item 推动传媒行业智能化转型
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth}
\begin{block>{学习方法建议}
    \begin{itemize>
        \item[\faBook] \textbf{理论学习：}深入理解AI技术原理
        \item[\faWrench] \textbf{实践操作：}大量动手练习和实验
        \item[\faLightbulb] \textbf{案例分析：}学习成功应用案例
        \item[\faHandshake] \textbf{同伴学习：}与同学交流讨论
        \item[\faGlobe] \textbf{持续关注：}跟踪行业最新发展
    \end{itemize>
\end{block>

\begin{block>{课程学习策略}
    \begin{itemize>
        \item[\faCheck] \textbf{积极参与：}主动参与课堂讨论和实践
        \item[\faEdit] \textbf{认真作业：}通过作业巩固和应用知识
        \item[\faSearch] \textbf{深入探索：}课后深入研究感兴趣的话题
        \item[\faComments] \textbf{多问多想：}遇到问题及时提问和思考
        \item[\faTarget] \textbf{项目导向：}以期末项目为目标整合学习
    \end{itemize>
\end{block>

\begin{alertblock>{成功要素}
    \begin{itemize>
        \item[\faFire] \textbf{保持热情：}对AI技术和传媒创新的热情
        \item[\faStrong] \textbf{持续努力：}坚持不懈的学习和实践
        \item[\faBrain] \textbf{开放心态：}拥抱新技术和新变化
        \item[\faTarget] \textbf{目标导向：}明确的学习目标和职业规划
        \item[\faHandshake] \textbf{合作精神：}与他人协作学习和成长
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns}
\end{frame>

% Interactive Learning Activities
\begin{frame}
\frametitle{互动学习：课堂参与活动}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{小组讨论活动}
\textbf{主题：AI对传媒职业的影响}
\begin{enumerate}
    \item \textbf{分组：}4-5人一组
    \item \textbf{讨论时间：}15分钟
    \item \textbf{讨论要点：}
    \begin{itemize}
        \item 哪些工作会被AI取代？
        \item 哪些工作会因AI而改变？
        \item 如何提升自己的竞争力？
    \end{itemize}
    \item \textbf{展示：}每组3分钟汇报
\end{enumerate}
\end{block}
\end{column}
\begin{column}{0.5\textwidth}
\begin{block}{角色扮演}
\textbf{场景：2030年的新闻编辑部}
\begin{itemize}
    \item[\faUser] \textbf{角色1：}传统记者
    \item[\faRobot] \textbf{角色2：}AI助手
    \item[\faUserTie] \textbf{角色3：}编辑主任
    \item[\faUsers] \textbf{角色4：}读者代表
\end{itemize}

\textbf{讨论话题：}如何平衡AI效率和人文关怀？
\end{block}

\begin{alertblock}{课堂互动工具}
    \begin{itemize}
        \item[\faMobile] Kahoot实时答题
        \item[\faComments] 弹幕提问系统
        \item[\faChartBar] 投票决策工具
        \item[\faShare] 协作文档共享
    \end{itemize}
\end{alertblock}
\end{column}
\end{columns}
\end{frame}

% Assessment Activities: Knowledge Check
\begin{frame}
\frametitle{知识检测：你准备好进入AI时代了吗？}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{快速自测题}
\textbf{1. 概念理解}
\begin{itemize}
    \item A. 机器学习的三种类型是什么？
    \item B. 深度学习和传统编程的最大区别？
    \item C. LLM的"幻觉"现象指什么？
\end{itemize}

\textbf{2. 应用判断}
\begin{itemize}
    \item A. 哪些新闻任务适合AI协助？
    \item B. AI生成内容需要注意什么？
    \item C. 如何评估AI工具的效果？
\end{itemize}
\end{block}
\end{column}
\begin{column}{0.5\textwidth}
\begin{alertblock}{实践作业}
\textbf{第1周作业：AI工具初体验}
\begin{enumerate}
    \item \textbf{选择工具：}ChatGPT、Claude、文心一言任选一个
    \item \textbf{完成任务：}
    \begin{itemize}
        \item 写一篇200字的科技新闻
        \item 为自己的专业写一个学习计划
        \item 分析一段用户评论的情感
    \end{itemize}
    \item \textbf{提交内容：}
    \begin{itemize}
        \item 体验报告（500字）
        \item 优缺点分析
        \item 改进建议
    \end{itemize}
\end{enumerate}
\end{alertblock}

\begin{exampleblock}{评分标准}
    \begin{itemize}
        \item[\faCheck] 完成度（40\%）
        \item[\faCheck] 分析深度（30\%）
        \item[\faCheck] 创新思考（20\%）
        \item[\faCheck] 表达清晰（10\%）
    \end{itemize}
\end{exampleblock}
\end{column}
\end{columns}
\end{frame}

% Next Week Preview
\begin{frame}
\frametitle{下周预告}
\begin{center}
\Large
\textbf{第2周内容：LLM工作原理与技术基础}
\end{center>

\begin{columns}
\begin{column>{0.5\textwidth}
\begin{block>{重点内容}
    \begin{itemize>
        \item[\faSearch] \textbf{Transformer架构：}深入理解注意力机制
        \item[\faWrench] \textbf{Tokenization：}文本预处理和编码
        \item[\faChartLine] \textbf{训练过程：}预训练和微调机制
        \item[\faTarget] \textbf{模型能力：}涌现能力和规模效应
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth}
\begin{block>{学习目标}
    \begin{itemize>
        \item 深入理解LLM的技术原理
        \item 掌握Transformer架构的核心概念
        \item 理解预训练和微调的机制
        \item 认识LLM的能力边界和应用场景
    \end{itemize>
\end{block>

\begin{alertblock>{课前准备}
    \begin{itemize>
        \item[\faBook] 阅读相关资料
        \item[\faUser] 注册AI平台账号
        \item[\faThink] 思考技术原理问题
        \item[\faEdit] 准备课堂讨论
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns}

\vspace{1cm}
\begin{center}
\Large
\textbf{感谢聆听！期待下周的精彩课程！}
\end{center>
\end{frame>

\end{document>