\input{../main_template.tex}

% Week 2 specific information
\title{AI驱动传媒内容制作}
\subtitle{第2周：LLM工作原理与技术基础}
\author{授课教师}
\institute{汕头大学 长江新闻与传播学院}
\date{\today}

\begin{document}

% Title slide
\begin{frame}
\titlepage
\end{frame}

% Week 1 Review
\begin{frame}
\frametitle{第1周内容回顾}
\begin{columns}
\begin{column}{0.5\textwidth}
\begin{block}{主要内容回顾}
    \begin{itemize}
        \item[\faTarget] \textbf{课程目标：}掌握AI大模型在传媒领域的应用
        \item[\faBook] \textbf{AI发展简史：}从达特茅斯会议到大模型时代
        \item[\faBrain] \textbf{机器学习基础：}监督学习、无监督学习、强化学习
        \item[\faRobot] \textbf{LLM概述：}大语言模型的定义和特征
        \item[\faNewspaper] \textbf{传媒应用：}AI在传媒各环节的应用潜力
    \end{itemize}
\end{block}

\begin{block}{关键概念}
    \begin{itemize}
        \item 人工智能的发展历程
        \item 机器学习的三种类型
        \item 深度学习与神经网络
        \item 大语言模型的突破性意义
        \item 数据驱动的学习范式
    \end{itemize}
\end{block}
\end{column}
\begin{column}{0.5\textwidth}
\begin{alertblock}{思考问题}
    \begin{itemize}
        \item[\faThinking] AI技术如何改变传媒行业？
        \item[\faThinking] 大语言模型与传统AI有什么区别？
        \item[\faThinking] 传媒人如何适应AI时代？
    \end{itemize}
\end{alertblock}
\end{column}
\end{columns}
\end{frame}

% Week 2 Learning Objectives
\begin{frame}
\frametitle{第2周学习目标与内容预览}
\begin{columns}
\begin{column}{0.6\textwidth}
\begin{block}{学习目标}
    \begin{itemize}
        \item[\faTarget] \textbf{了解Transformer架构}的基本原理和注意力机制
        \item[\faTarget] \textbf{理解Tokenization概念}及其对模型的影响
        \item[\faTarget] \textbf{掌握LLM的训练过程：}预训练、微调、RLHF
        \item[\faTarget] \textbf{认识LLM的能力边界}与局限性
    \end{itemize}
\end{block}

\begin{block}{内容安排}
    \begin{enumerate}
        \item \textbf{Transformer架构简介}（10页）
        \item \textbf{Tokenization概念}（4页）
        \item \textbf{LLM训练过程}（8页）
        \item \textbf{能力边界分析}（4页）
    \end{enumerate}
\end{block}
\end{column}
\begin{column}{0.4\textwidth}
\begin{alertblock}{重点难点}
    \begin{itemize}
        \item[\faExclamationTriangle] 注意力机制的理解
        \item[\faExclamationTriangle] 训练过程的复杂性
        \item[\faExclamationTriangle] 能力边界的准确认知
    \end{itemize}
\end{alertblock}
\end{column}
\end{columns}
\end{frame>

% Section: Transformer Architecture
\section{Transformer架构}

% Why Transformer?
\begin{frame>
\frametitle{从RNN到Transformer：架构演进的必然}
\begin{columns}
\begin{column>{0.5\textwidth}
\begin{alertblock}{传统RNN的局限性}
    \begin{itemize>
        \item[\faSnail] \textbf{序列处理：}必须按顺序处理，无法并行
        \item[\faChartDown] \textbf{长距离依赖：}难以捕捉长序列中的远距离关系
        \item[\faSave] \textbf{梯度问题：}梯度消失和梯度爆炸
        \item[\faClock] \textbf{训练效率：}训练时间长，计算效率低
    \end{itemize>
\end{alertblock>

\begin{block>{LSTM的改进与不足}
    \textbf{改进：}通过门控机制缓解梯度问题\\
    \textbf{不足：}序列处理限制依然存在，结构复杂
\end{block>
\end{column>
\begin{column>{0.5\textwidth}
\begin{block>{Transformer的突破}
    \begin{itemize>
        \item[\faBolt] \textbf{并行处理：}所有位置可以同时计算
        \item[\faTarget] \textbf{长距离依赖：}直接建模任意位置间的关系
        \item[\faRocket] \textbf{训练效率：}大幅提升训练速度
        \item[\faPalette] \textbf{简洁优雅：}架构相对简单，易于理解和实现
    \end{itemize>
\end{block>

\begin{block>{影响深远}
    \begin{itemize>
        \item[\faChartLine] 在多个NLP任务上取得突破
        \item[\faBuilding] 成为现代大模型的基础架构
        \item[\faGlobe] 从NLP扩展到计算机视觉等领域
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Attention Mechanism Intuition
\begin{frame>
\frametitle{注意力机制：模拟人类的注意力}
\begin{columns>
\begin{column>{0.5\textwidth}
\begin{block>{人类注意力的特点}
    \begin{itemize>
        \item[\faEye] \textbf{选择性关注：}在复杂环境中聚焦重要信息
        \item[\faTarget] \textbf{动态调整：}根据任务需求调整注意力分配
        \item[\faBrain] \textbf{并行处理：}同时处理多个信息源
        \item[\faLightbulb] \textbf{上下文相关：}基于上下文决定关注重点
    \end{itemize>
\end{block>

\begin{block>{机器注意力机制}
    \begin{itemize>
        \item[\faChartBar] \textbf{权重分配：}为不同位置分配不同的注意力权重
        \item[\faSearch] \textbf{相关性计算：}计算查询与键之间的相关性
        \item[\faChartLine] \textbf{加权求和：}基于权重对值进行加权平均
        \item[\faTarget] \textbf{动态聚焦：}根据输入动态调整关注重点
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth}
\begin{block>{注意力机制的优势}
    \begin{itemize>
        \item[\faGlobe] \textbf{全局视野：}能够同时关注序列中的所有位置
        \item[\faTarget] \textbf{精准定位：}准确识别重要信息的位置
        \item[\faRefresh] \textbf{灵活适应：}根据不同任务调整注意力模式
        \item[\faChartBar] \textbf{可解释性：}注意力权重提供模型决策的可视化
    \end{itemize>
\end{block>

\begin{block>{生活中的类比}
    \begin{itemize>
        \item[\faBook] \textbf{阅读理解：}在阅读时关注关键词和重要句子
        \item[\faMusic] \textbf{听音乐：}在复杂音乐中聚焦主旋律
        \item[\faCar] \textbf{开车：}同时关注路况、信号灯、行人等
        \item[\faUsers] \textbf{对话：}在嘈杂环境中专注于对话者的声音
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Self-Attention Mechanism
\begin{frame>
\frametitle{Self-Attention：序列内部的关系建模}
\begin{columns>
\begin{column>{0.5\textwidth}
\begin{block>{Self-Attention的核心思想}
    \begin{itemize>
        \item[\faSearch] \textbf{自我关注：}序列中每个位置关注其他所有位置
        \item[\faGlobe] \textbf{全局连接：}直接建模任意两个位置间的关系
        \item[\faChartBar] \textbf{权重计算：}动态计算每个位置的重要性权重
        \item[\faTarget] \textbf{信息整合：}基于权重整合全局信息
    \end{itemize>
\end{block>

\begin{block>{三个关键概念}
    \begin{itemize>
        \item[\faSearch] \textbf{Query（查询）：}当前位置想要查找的信息
        \item[\faKey] \textbf{Key（键）：}每个位置提供的索引信息
        \item[\faGem] \textbf{Value（值）：}每个位置包含的实际内容
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth}
\begin{block>{计算过程}
\begin{enumerate}
    \item 计算相似度：Attention Score = Query × Key$^T$
    \item 归一化权重：Attention Weight = Softmax(Score)
    \item 加权求和：Output = Attention Weight × Value
\end{enumerate>
\end{block>

\begin{block>{数学公式}
\begin{center>
$\text{Attention}(Q,K,V) = \text{Softmax}\left(\frac{QK^T}{\sqrt{d_k}}\right)V$
\end{center>
\end{block>

\begin{block>{优势特点}
    \begin{itemize>
        \item[\faBolt] \textbf{并行计算：}所有位置可以同时计算
        \item[\faTarget] \textbf{长距离建模：}直接连接远距离位置
        \item[\faRefresh] \textbf{动态权重：}根据输入内容动态调整
        \item[\faChartBar] \textbf{可解释性：}注意力权重可视化
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Attention Visualization with TikZ - ENRICHMENT
\begin{frame}
\frametitle{注意力机制可视化：实际案例分析}
\begin{columns}
\begin{column}{0.6\textwidth}
\begin{block}{案例句子："AI正在改变新闻行业"}
\begin{center}
\begin{tikzpicture}[scale=0.7]
    % Define styles
    \tikzstyle{word}=[circle,draw,minimum size=1cm,fill=lightgray]
    \tikzstyle{focus}=[circle,draw,minimum size=1cm,fill=yellow]
    
    % Define nodes for each word
    \node[word] (AI) at (0,0) {AI};
    \node[word] (正在) at (1.8,0) {正在};
    \node[focus] (改变) at (3.6,0) {改变};
    \node[word] (新闻) at (5.4,0) {新闻};
    \node[word] (行业) at (7.2,0) {行业};
    
    % Attention arrows with weights
    \draw[->, thick, red, bend left=30] (改变) to node[above] {\small 0.3} (AI);
    \draw[->, thick, blue, bend left=20] (改变) to node[above] {\small 0.4} (正在);
    \draw[->, thick, green, bend right=30] (改变) to node[above] {\small 0.2} (新闻);
    \draw[->, thick, purple, bend right=20] (改变) to node[above] {\small 0.1} (行业);
    
    \node[below] at (3.6,-1) {\small "改变"的注意力分布};
\end{tikzpicture}
\end{center}
\end{block}

\begin{block}{权重解释与分析}
\begin{itemize}
\item[\faArrowRight] \textbf{0.4 → "正在"}：动态时态信息最重要
\item[\faArrowRight] \textbf{0.3 → "AI"}：行为主体次重要
\item[\faArrowRight] \textbf{0.2 → "新闻"}：作用对象有一定关注
\item[\faArrowRight] \textbf{0.1 → "行业"}：范围限定关注度最低
\end{itemize}
\end{block}
\end{column}

\begin{column}{0.4\textwidth}
\begin{alertblock}{互动练习}
\textbf{新句子：}"记者使用AI写作工具"

\vspace{0.2cm}
\textbf{分析任务：}
\begin{enumerate}
\item 预测"使用"的注意力分布
\item 讨论"AI"与其他词的关系
\item 分析"工具"的语义连接
\end{enumerate}

\vspace{0.2cm}
\textbf{讨论时间：}5分钟小组讨论
\end{alertblock}

\begin{exampleblock}{传媒应用启示}
\textbf{在新闻理解中，注意力帮助AI：}
\begin{itemize}
\item[\faNewspaper] 识别新闻5W1H要素
\item[\faLink] 建立事件间的因果关系
\item[\faUsers] 理解不同角色的重要性
\item[\faChartBar] 评估信息的重要程度
\end{itemize}
\end{exampleblock}
\end{column}
\end{columns}
\end{frame>

% Transformer Architecture Diagram - ENRICHMENT  
\begin{frame}
\frametitle{Transformer完整架构图：编码器-解码器结构}
\begin{center}
\begin{tikzpicture}[scale=0.6]
    % Encoder stack
    \draw[thick] (0,0) rectangle (3,6);
    \node at (1.5,5.5) {\textbf{编码器}};
    
    % Encoder layers
    \draw (0.2,4.5) rectangle (2.8,5.3);
    \node[scale=0.7] at (1.5,4.9) {Multi-Head Attention};
    
    \draw (0.2,3.5) rectangle (2.8,4.3);
    \node[scale=0.7] at (1.5,3.9) {Feed Forward};
    
    \draw (0.2,2.5) rectangle (2.8,3.3);
    \node[scale=0.7] at (1.5,2.9) {Multi-Head Attention};
    
    \draw (0.2,1.5) rectangle (2.8,2.3);
    \node[scale=0.7] at (1.5,1.9) {Feed Forward};
    
    \draw (0.2,0.5) rectangle (2.8,1.3);
    \node[scale=0.7] at (1.5,0.9) {Positional Encoding};
    
    % Decoder stack
    \draw[thick] (5,0) rectangle (8,6);
    \node at (6.5,5.5) {\textbf{解码器}};
    
    % Decoder layers
    \draw (5.2,4.5) rectangle (7.8,5.3);
    \node[scale=0.7] at (6.5,4.9) {Masked Attention};
    
    \draw (5.2,3.5) rectangle (7.8,4.3);
    \node[scale=0.7] at (6.5,3.9) {Cross Attention};
    
    \draw (5.2,2.5) rectangle (7.8,3.3);
    \node[scale=0.7] at (6.5,2.9) {Feed Forward};
    
    \draw (5.2,1.5) rectangle (7.8,2.3);
    \node[scale=0.7] at (6.5,1.9) {Output Layer};
    
    \draw (5.2,0.5) rectangle (7.8,1.3);
    \node[scale=0.7] at (6.5,0.9) {Positional Encoding};
    
    % Connections
    \draw[->, thick] (3,3) -- (5,3.5);
    \node[above] at (4,3.3) {\small 编码信息};
    
    % Input/Output
    \node[below] at (1.5,-0.5) {\textbf{输入文本}};
    \node[below] at (6.5,-0.5) {\textbf{目标文本}};
    \node[above] at (6.5,6.5) {\textbf{输出文本}};
    
    % Arrows
    \draw[->, thick] (1.5,-0.3) -- (1.5,0.3);
    \draw[->, thick] (6.5,-0.3) -- (6.5,0.3);
    \draw[->, thick] (6.5,5.7) -- (6.5,6.3);
\end{tikzpicture}
\end{center}

\begin{alertblock}{架构关键点}
\begin{itemize}
\item[\faLayers] \textbf{堆叠结构}：多层编码器和解码器堆叠
\item[\faExchange] \textbf{跨层连接}：编码器输出传递给所有解码器层  
\item[\faMask] \textbf{掩码机制}：解码器使用掩码防止看到未来信息
\item[\faRefresh] \textbf{残差连接}：每个子层都有残差连接和层归一化
\end{itemize}
\end{alertblock}

\end{frame>

% Multi-Head Attention
\begin{frame>
\frametitle{Multi-Head Attention：多角度的信息捕捉}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{为什么需要多头注意力？}
    \begin{itemize>
        \item[\faTarget] \textbf{多样化关注：}从不同角度关注信息
        \item[\faBrain] \textbf{丰富表示：}捕捉更丰富的语义关系
        \item[\faRefresh] \textbf{并行处理：}多个注意力头并行工作
        \item[\faChartBar] \textbf{性能提升：}显著提升模型表现
    \end{itemize>
\end{block>

\begin{block>{工作原理}
    \begin{enumerate>
        \item \textbf{线性变换：}将输入投影到多个子空间
        \item \textbf{并行计算：}每个头独立计算注意力
        \item \textbf{结果拼接：}将所有头的输出拼接
        \item \textbf{最终投影：}通过线性层得到最终输出
    \end{enumerate>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{不同头的作用}
    \begin{itemize>
        \item[\faTarget] \textbf{语法关系：}某些头专注于语法结构
        \item[\faThinking] \textbf{语义关系：}某些头关注语义相关性
        \item[\faMapMarker] \textbf{位置关系：}某些头捕捉位置信息
        \item[\faLink] \textbf{长距离依赖：}某些头建模远距离关系
    \end{itemize>
\end{block>

\begin{block>{类比理解}
    \begin{itemize>
        \item[\faUsers] \textbf{团队协作：}不同专家从不同角度分析问题
        \item[\faPalette] \textbf{多角度观察：}从多个视角观察同一个物体
        \item[\faChartBar] \textbf{多维分析：}从多个维度分析数据
        \item[\faSearch] \textbf{多重检查：}多次检查确保准确性
    \end{itemize>
\end{block>

\begin{alertblock>{实际效果}
    \begin{itemize>
        \item[\faChartLine] 相比单头注意力显著提升
        \item[\faTarget] 不同头学会专注不同类型的关系
        \item[\faRefresh] 多头机制提供冗余和鲁棒性
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% Positional Encoding
\begin{frame>
\frametitle{位置编码：为Transformer注入序列信息}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{位置信息的重要性}
    \begin{itemize>
        \item[\faMapMarker] \textbf{序列顺序：}语言中词序对意义至关重要
        \item[\faRefresh] \textbf{时间关系：}事件的时间顺序影响理解
        \item[\faChartBar] \textbf{结构信息：}句法结构依赖位置关系
        \item[\faTarget] \textbf{语义区分：}相同词在不同位置意义不同
    \end{itemize>
\end{block>

\begin{alertblock>{Transformer的位置编码挑战}
    \begin{itemize>
        \item[\faTimes] \textbf{天然无序：}Self-Attention机制本身不考虑位置
        \item[\faRefresh] \textbf{置换不变性：}打乱输入顺序结果相同
        \item[\faChartBar] \textbf{需要补充：}必须显式添加位置信息
    \end{itemize>
\end{alertblock>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{位置编码的设计原则}
    \begin{itemize>
        \item[\faRuler] \textbf{唯一性：}每个位置有唯一的编码
        \item[\faRefresh] \textbf{相对关系：}能够表示位置间的相对关系
        \item[\faChartBar] \textbf{可扩展性：}能够处理不同长度的序列
        \item[\faBolt] \textbf{计算效率：}编码计算要高效
    \end{itemize>
\end{block>

\begin{block>{正弦位置编码}
\begin{center>
$PE_{(pos, 2i)} = \sin(pos/10000^{2i/d_{model}})$\\
$PE_{(pos, 2i+1)} = \cos(pos/10000^{2i/d_{model}})$
\end{center>
\end{block>

\begin{block>{正弦编码的优势}
    \begin{itemize>
        \item[\faWave] \textbf{周期性：}不同频率的正弦波组合
        \item[\faRuler] \textbf{相对位置：}能够表示相对位置关系
        \item[\faRefresh] \textbf{外推能力：}可以处理训练时未见过的长度
        \item[\faBolt] \textbf{计算简单：}无需学习参数，直接计算
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Complete Transformer Architecture
\begin{frame>
\frametitle{Transformer架构全貌}
\begin{columns>
\begin{column>{0.4\textwidth>
\begin{center>
\begin{tikzpicture>[scale=0.6]
    % Input
    \node[box] (input) at (0,0) {输入};
    \node[box] (embed) at (0,-1) {嵌入层};
    \node[box] (pos) at (0,-2) {位置编码};
    
    % Encoder
    \node[box] (enc1) at (0,-3.5) {Encoder层};
    \node at (0.7,-3.5) {×N};
    
    % Decoder
    \node[box] (dec1) at (0,-5) {Decoder层};
    \node at (0.7,-5) {×N};
    
    % Output
    \node[box] (linear) at (0,-6.5) {线性层};
    \node[box] (softmax) at (0,-7.5) {Softmax};
    \node[box] (output) at (0,-8.5) {输出};
    
    % Arrows
    \draw[arrow] (input) -- (embed);
    \draw[arrow] (embed) -- (pos);
    \draw[arrow] (pos) -- (enc1);
    \draw[arrow] (enc1) -- (dec1);
    \draw[arrow] (dec1) -- (linear);
    \draw[arrow] (linear) -- (softmax);
    \draw[arrow] (softmax) -- (output);
\end{tikzpicture>
\end{center>
\end{column>
\begin{column>{0.6\textwidth>
\begin{block>{Encoder层结构}
    \begin{itemize>
        \item[\faSearch] \textbf{Multi-Head Attention：}多头自注意力机制
        \item[\faPlus] \textbf{残差连接：}Add \& Norm操作
        \item[\faBrain] \textbf{Feed Forward：}前馈神经网络
        \item[\faPlus] \textbf{残差连接：}Add \& Norm操作
    \end{itemize>
\end{block>

\begin{block>{Decoder层结构}
    \begin{itemize>
        \item[\faMask] \textbf{Masked Multi-Head Attention：}掩码自注意力
        \item[\faPlus] \textbf{残差连接：}Add \& Norm操作
        \item[\faLink] \textbf{Cross Attention：}编码器-解码器注意力
        \item[\faPlus] \textbf{残差连接：}Add \& Norm操作
        \item[\faBrain] \textbf{Feed Forward：}前馈神经网络
        \item[\faPlus] \textbf{残差连接：}Add \& Norm操作
    \end{itemize>
\end{block>

\begin{block>{关键组件详解}
    \begin{itemize>
        \item[\faRefresh] \textbf{残差连接：}缓解深层网络的梯度消失问题
        \item[\faChartBar] \textbf{层归一化：}稳定训练过程，加速收敛
        \item[\faBrain] \textbf{前馈网络：}增加模型的非线性表达能力
        \item[\faMask] \textbf{掩码机制：}防止解码器看到未来信息
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Encoder-Decoder vs Decoder-Only
\begin{frame>
\frametitle{两种主流架构：各有所长}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{Encoder-Decoder架构}
    \begin{itemize>
        \item[\faBuilding] \textbf{结构特点：}编码器+解码器的双塔结构
        \item[\faTarget] \textbf{适用任务：}序列到序列的转换任务
        \item[\faLightbulb] \textbf{工作原理：}编码器理解输入，解码器生成输出
        \item[\faChartBar] \textbf{代表模型：}T5、BART、机器翻译模型
    \end{itemize}
\end{block>

\begin{block}{优势}
    \begin{itemize>
        \item[\faTarget] 任务专门化：编码和解码功能分离
        \item[\faRefresh] 双向理解：编码器可以双向理解输入
        \item[\faChartBar] 结构清晰：输入输出处理逻辑清晰
        \item[\faPalette] 灵活性：可以处理不同长度的输入输出
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{Decoder-Only架构}
    \begin{itemize>
        \item[\faBuilding] \textbf{结构特点：}只有解码器的单塔结构
        \item[\faTarget] \textbf{适用任务：}自回归文本生成
        \item[\faLightbulb] \textbf{工作原理：}逐词预测下一个词
        \item[\faChartBar] \textbf{代表模型：}GPT系列、LLaMA、PaLM
    \end{itemize>
\end{block>

\begin{block>{优势}
    \begin{itemize>
        \item[\faBolt] 训练效率：结构简单，训练更高效
        \item[\faTarget] 生成能力：专门优化文本生成任务
        \item[\faChartLine] 可扩展性：更容易扩展到大规模
        \item[\faRefresh] 统一框架：用一个框架处理多种任务
    \end{itemize}
\end{block>

\begin{alertblock>{发展趋势}
    \begin{itemize>
        \item[\faChartLine] Decoder-Only主导：大模型时代的主流选择
        \item[\faRefresh] 架构融合：结合两种架构的优势
        \item[\faRocket] 持续创新：新的架构变体不断涌现
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% Transformer Advantages and Impact
\begin{frame>
\frametitle{Transformer：改变AI的架构革命}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{技术优势总结}
    \begin{itemize>
        \item[\faBolt] \textbf{并行计算：}大幅提升训练和推理效率
        \item[\faTarget] \textbf{长距离建模：}有效捕捉长序列中的依赖关系
        \item[\faChartBar] \textbf{可解释性：}注意力权重提供模型决策的可视化
        \item[\faRefresh] \textbf{架构简洁：}相对简单的结构，易于理解和实现
        \item[\faChartLine] \textbf{性能卓越：}在多个任务上取得突破性进展
    \end{itemize>
\end{block>

\begin{block>{对AI领域的深远影响}
    \begin{itemize>
        \item[\faBuilding] \textbf{架构基础：}成为现代大模型的标准架构
        \item[\faGlobe] \textbf{跨领域应用：}从NLP扩展到CV、语音等领域
        \item[\faChartLine] \textbf{性能突破：}推动多个AI任务达到新高度
        \item[\faMicroscope] \textbf{研究方向：}引导AI研究的新方向
    \end{itemize}
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{在NLP领域的革命}
    \begin{itemize>
        \item[\faBook] \textbf{预训练范式：}推动预训练+微调的发展
        \item[\faTarget] \textbf{多任务学习：}一个模型处理多种NLP任务
        \item[\faGlobe] \textbf{多语言模型：}支持多语言的统一模型
        \item[\faLightbulb] \textbf{少样本学习：}强大的少样本和零样本能力
    \end{itemize>
\end{block>

\begin{block}{对传媒行业的意义}
    \begin{itemize>
        \item[\faNewspaper] \textbf{内容生成：}高质量的自动内容生成
        \item[\faSearch] \textbf{信息处理：}强大的文本理解和分析能力
        \item[\faGlobe] \textbf{多语言支持：}跨语言的内容处理
        \item[\faTarget] \textbf{个性化服务：}基于深度理解的个性化推荐
    \end{itemize>
\end{block>

\begin{alertblock>{未来发展方向}
    \begin{itemize>
        \item[\faChartBar] 效率优化：减少计算复杂度，提高效率
        \item[\faTarget] 架构创新：探索新的注意力机制和架构
        \item[\faGlobe] 多模态融合：结合文本、图像、音频等模态
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% Attention Visualization
\begin{frame>
\frametitle{看见AI的"思考"：注意力可视化}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{注意力可视化的价值}
    \begin{itemize>
        \item[\faEye] \textbf{透明性：}让AI的决策过程可见
        \item[\faSearch] \textbf{调试工具：}帮助发现模型的问题
        \item[\faBook] \textbf{教学辅助：}帮助理解模型工作原理
        \item[\faTarget] \textbf{应用指导：}指导模型的实际应用
    \end{itemize>
\end{block>

\begin{block}{可视化示例1：句子内部关系}
\textbf{输入句子：}"The cat sat on the mat"\\
\textbf{可视化显示：}
\begin{itemize}
    \item "cat" 强烈关注 "sat"（主谓关系）
    \item "sat" 关注 "on"（动词介词关系）
    \item "on" 关注 "mat"（介词宾语关系）
\end{itemize}
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{可视化示例2：长距离依赖}
\textbf{输入句子：}"The book that I bought yesterday is very interesting"\\
\textbf{可视化显示：}
\begin{itemize>
    \item "book" 与 "interesting" 有强连接
    \item "I" 与 "bought" 有强连接
    \item 跨越中间词汇的长距离关系
\end{itemize>
\end{block>

\begin{block>{多头注意力分工}
\textbf{不同注意力头的专门化：}
\begin{itemize>
    \item Head 1：关注语法关系（主谓宾）
    \item Head 2：关注语义相关性
    \item Head 3：关注位置邻近性
    \item Head 4：关注长距离依赖
\end{itemize}
\end{block>

\begin{alertblock>{在传媒应用中的价值}
    \begin{itemize>
        \item[\faNewspaper] 理解AI如何理解新闻内容
        \item[\faTarget] 检查AI是否关注了正确的信息
        \item[\faChartBar] 评估AI处理的准确性
    \end{itemize}
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% From Transformer to LLM
\begin{frame>
\frametitle{架构演进：从Transformer到LLM}
\begin{center>
\begin{tikzpicture>[scale=0.8]
    \draw[thick,->] (0,0) -- (12,0);
    \foreach \x/\model in {1/Transformer,3/BERT,5/GPT-1,7/GPT-2,9/GPT-3,11/GPT-4}
        \draw (\x,0.1) -- (\x,-0.1) node[below] {\model};
    
    \node[above] at (1,0.3) {2017};
    \node[above] at (3,0.3) {2018};
    \node[above] at (5,0.3) {2018};
    \node[above] at (7,0.3) {2019};
    \node[above] at (9,0.3) {2020};
    \node[above] at (11,0.3) {2023};
\end{tikzpicture>
\end{center>

\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{关键演进节点}
    \begin{itemize>
        \item[\faBuilding] \textbf{2017年 Transformer：}奠定架构基础
        \item[\faTarget] \textbf{2018年 BERT：}双向编码器的突破
        \item[\faEdit] \textbf{2018年 GPT-1：}生成式预训练的开端
        \item[\faRocket] \textbf{2019年 GPT-2：}规模扩大的质变
        \item[\faGlobe] \textbf{2020年 GPT-3：}涌现能力的显现
        \item[\faComments] \textbf{2022年 ChatGPT：}对话能力的突破
        \item[\faPalette] \textbf{2023年 GPT-4：}多模态的融合
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{规模演进}
    \begin{itemize>
        \item[\faChartBar] \textbf{参数量增长：}从百万级到千亿级
        \item[\faBook] \textbf{数据规模：}从GB级到TB级训练数据
        \item[\faBolt] \textbf{计算需求：}从单GPU到大规模集群
        \item[\faTarget] \textbf{能力提升：}从单任务到通用智能
    \end{itemize>
\end{block>

\begin{block>{能力演进}
    \begin{itemize>
        \item[\faBook] \textbf{理解能力：}从简单分类到深度理解
        \item[\faEdit] \textbf{生成能力：}从模板填充到创意写作
        \item[\faCalculator] \textbf{推理能力：}从简单匹配到复杂推理
        \item[\faTarget] \textbf{泛化能力：}从特定任务到通用能力
    \end{itemize>
\end{block>

\begin{alertblock>{对传媒的影响}
    \begin{itemize>
        \item[\faNewspaper] 内容创作：从辅助工具到创作伙伴
        \item[\faSearch] 信息处理：从简单分类到深度分析
        \item[\faComments] 用户交互：从关键词搜索到自然对话
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% Section: Tokenization
\section{Tokenization}

% What is Token?
\begin{frame>
\frametitle{Token：AI理解语言的基本单位}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{Token的定义}
    \begin{itemize>
        \item[\faPuzzlePiece] \textbf{基本单位：}AI模型处理文本的最小单位
        \item[\faHashtag] \textbf{数字表示：}将文本转换为数字序列
        \item[\faChartBar] \textbf{统一格式：}不同语言和符号的统一表示
        \item[\faTarget] \textbf{模型输入：}神经网络的实际输入格式
    \end{itemize>
\end{block>

\begin{block>{为什么需要Tokenization？}
    \begin{itemize>
        \item[\faRobot] \textbf{机器理解：}计算机只能处理数字，不能直接处理文字
        \item[\faChartBar] \textbf{统一处理：}将不同类型的文本统一为数字序列
        \item[\faTarget] \textbf{效率考虑：}合适的分词能提高处理效率
        \item[\faBrain] \textbf{语义保持：}尽可能保持原文的语义信息
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{Token的类型}
    \begin{itemize>
        \item[\faFont] \textbf{字符级：}每个字符是一个Token
        \item[\faEdit] \textbf{词级：}每个单词是一个Token
        \item[\faPuzzlePiece] \textbf{子词级：}介于字符和单词之间
        \item[\faTarget] \textbf{句子级：}整个句子作为一个Token
    \end{itemize>
\end{block>

\begin{block>{实际例子}
\textbf{原文：}"Hello, world!"
\begin{itemize>
    \item \textbf{字符级：}["H","e","l","l","o",",","w","o","r","l","d","!"]
    \item \textbf{词级：}["Hello",",","world","!"]
    \item \textbf{子词级：}["Hello",",","world","!"] 或 ["Hel","lo",",","wor","ld","!"]
\end{itemize}
\end{block>

\begin{alertblock>{Token化的影响}
    \begin{itemize>
        \item[\faTarget] 词汇表大小：影响模型的参数量
        \item[\faChartBar] 序列长度：影响模型的计算复杂度
        \item[\faBrain] 语义理解：影响模型对语言的理解
        \item[\faBolt] 处理效率：影响训练和推理速度
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% Tokenization Methods Comparison
\begin{frame>
\frametitle{分词策略：各有优劣的选择}
\begin{columns>
\begin{column>{0.33\textwidth>
\begin{block}{字符级分词}
\textbf{Character-level}

\textbf{优点：}
\begin{itemize>
    \item[\faCheck] 词汇表小，参数少
    \item[\faCheck] 没有未知词问题
    \item[\faCheck] 适合处理拼写错误
    \item[\faCheck] 支持任意语言
\end{itemize>

\textbf{缺点：}
\begin{itemize>
    \item[\faTimes] 序列长度很长
    \item[\faTimes] 难以捕捉词级语义
    \item[\faTimes] 计算效率低
    \item[\faTimes] 训练困难
\end{itemize}
\end{block>
\end{column>
\begin{column>{0.33\textwidth>
\begin{block>{词级分词}
\textbf{Word-level}

\textbf{优点：}
\begin{itemize>
    \item[\faCheck] 保持词的完整语义
    \item[\faCheck] 序列长度适中
    \item[\faCheck] 符合人类理解习惯
    \item[\faCheck] 处理效率高
\end{itemize}

\textbf{缺点：}
\begin{itemize>
    \item[\faTimes] 词汇表巨大
    \item[\faTimes] 存在未知词问题
    \item[\faTimes] 难处理形态变化
    \item[\faTimes] 不同语言差异大
\end{itemize>
\end{block>
\end{column>
\begin{column>{0.33\textwidth>
\begin{block>{子词级分词}
\textbf{Subword-level}

\textbf{优点：}
\begin{itemize>
    \item[\faCheck] 平衡词汇表大小和语义
    \item[\faCheck] 处理未知词能力强
    \item[\faCheck] 适应多种语言
    \item[\faCheck] 现代模型的主流选择
\end{itemize>

\textbf{缺点：}
\begin{itemize>
    \item[\faTimes] 分词结果不够直观
    \item[\faTimes] 需要额外的分词算法
    \item[\faTimes] 可能破坏词的完整性
\end{itemize>
\end{block>
\end{column>
\end{columns>

\begin{block>{主流子词分词算法}
    \begin{itemize>
        \item[\faWrench] \textbf{BPE（Byte Pair Encoding）：}基于频率的合并算法
        \item[\faTarget] \textbf{WordPiece：}Google开发，BERT使用
        \item[\faChartBar] \textbf{SentencePiece：}支持多语言的统一分词
        \item[\faRocket] \textbf{Unigram：}基于概率的分词方法
    \end{itemize>
\end{block>
\end{frame>

% BPE Algorithm
\begin{frame>
\frametitle{BPE：现代LLM的主流分词算法}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{BPE算法原理}
    \begin{itemize>
        \item[\faFont] \textbf{初始状态：}从字符级开始
        \item[\faChartBar] \textbf{统计频率：}统计相邻字符对的出现频率
        \item[\faRefresh] \textbf{迭代合并：}反复合并最频繁的字符对
        \item[\faTarget] \textbf{构建词汇表：}逐步构建子词词汇表
    \end{itemize>
\end{block>

\begin{block>{算法步骤}
    \begin{enumerate}
        \item \textbf{初始化：}将所有文本分解为字符
        \item \textbf{统计：}计算所有相邻字符对的频率
        \item \textbf{合并：}合并频率最高的字符对
        \item \textbf{更新：}更新文本和频率统计
        \item \textbf{重复：}重复步骤2-4直到达到目标词汇表大小
    \end{enumerate>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block}{BPE示例}
\textbf{初始文本：}["low","lower","newest","widest"]\\
\textbf{字符级：}["l o w","l o w e r","n e w e s t","w i d e s t"]

\textbf{迭代1：}合并最频繁的"e s"→"es"\\
\textbf{结果：}["l o w","l o w e r","n e w es t","w i d es t"]

\textbf{迭代2：}合并最频繁的"es t"→"est"\\
\textbf{结果：}["l o w","l o w e r","n e w est","w i d est"]

\textbf{继续迭代...}
\end{block>

\begin{block>{BPE的优势}
    \begin{itemize>
        \item[\faChartBar] \textbf{数据驱动：}基于实际数据的频率统计
        \item[\faTarget] \textbf{平衡性：}平衡词汇表大小和语义保持
        \item[\faRefresh] \textbf{适应性：}能够适应不同的语言和领域
        \item[\faBolt] \textbf{效率：}算法简单，计算效率高
    \end{itemize}
\end{block>

\begin{alertblock>{在LLM中的应用}
    \begin{itemize>
        \item[\faRobot] GPT系列：使用BPE进行分词
        \item[\faChartBar] 词汇表大小：通常在30K-50K之间
        \item[\faGlobe] 多语言支持：支持多种语言的统一处理
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% Token Impact on Model Understanding
\begin{frame>
\frametitle{分词的艺术：如何影响AI的理解}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{Token粒度对理解的影响}
    \textbf{细粒度（字符级）：}
    \begin{itemize>
        \item 能够处理任意文本
        \item 但难以理解词汇语义
        \item 需要更多计算来组合语义
    \end{itemize>
    
    \textbf{中粒度（子词级）：}
    \begin{itemize}
        \item 平衡语义和灵活性
        \item 现代LLM的主流选择
        \item 能够处理大多数情况
    \end{itemize>
    
    \textbf{粗粒度（词级）：}
    \begin{itemize>
        \item 保持完整词汇语义
        \item 但词汇表过大
        \item 存在未知词问题
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{具体影响案例}
    \textbf{专业术语：}
    \begin{itemize}
        \item 好的分词：["machine","learning"]
        \item 差的分词：["mach","ine","learn","ing"]
        \item 影响：语义理解的准确性
    \end{itemize}
    
    \textbf{多语言文本：}
    \begin{itemize}
        \item 中文：需要考虑词汇边界
        \item 英文：相对容易分词
        \item 影响：跨语言理解能力
    \end{itemize>
    
    \textbf{数字和符号：}
    \begin{itemize}
        \item 数字分词：影响数值理解
        \item 特殊符号：影响格式理解
        \item 影响：结构化信息处理
    \end{itemize>
\end{block>

\begin{alertblock>{在传媒应用中的考虑}
    \begin{itemize>
        \item[\faNewspaper] 新闻文本：需要处理专业术语和人名地名
        \item[\faMobile] 社交媒体：需要处理网络用语和表情符号
        \item[\faGlobe] 多语言内容：需要统一的多语言分词策略
        \item[\faChartBar] 结构化数据：需要保持数据格式的完整性
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% Section: LLM Training Process
\section{LLM训练过程}

% LLM Training Overview
\begin{frame>
\frametitle{LLM训练：从数据到智能的转化}
\begin{center>
\begin{tikzpicture>[scale=0.8]
    \node[box] (data) at (0,0) {原始数据};
    \node[box] (preprocess) at (2,0) {数据预处理};
    \node[box] (pretrain) at (4,0) {预训练};
    \node[box] (finetune) at (6,0) {微调};
    \node[box] (rlhf) at (8,0) {RLHF};
    \node[box] (deploy) at (10,0) {部署应用};
    
    \draw[arrow] (data) -- (preprocess);
    \draw[arrow] (preprocess) -- (pretrain);
    \draw[arrow] (pretrain) -- (finetune);
    \draw[arrow] (finetune) -- (rlhf);
    \draw[arrow] (rlhf) -- (deploy);
\end{tikzpicture>
\end{center>

\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{三个主要阶段}
    \textbf{1. 预训练（Pre-training）}
    \begin{itemize>
        \item[\faTarget] 目标：学习语言的基本规律和知识
        \item[\faBook] 数据：大规模无标注文本数据
        \item[\faRefresh] 方法：自监督学习，预测下一个词
        \item[\faClock] 时间：数周到数月
    \end{itemize>
    
    \textbf{2. 微调（Fine-tuning）}
    \begin{itemize>
        \item[\faTarget] 目标：适应特定任务或领域
        \item[\faChartBar] 数据：少量高质量标注数据
        \item[\faWrench] 方法：有监督学习，任务特定优化
        \item[\faClock] 时间：数小时到数天
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
    \textbf{3. 人类反馈强化学习（RLHF）}
    \begin{itemize>
        \item[\faTarget] 目标：对齐人类价值观和偏好
        \item[\faUsers] 数据：人类标注的偏好数据
        \item[\faGamepad] 方法：强化学习，奖励模型指导
        \item[\faClock] 时间：数天到数周
    \end{itemize>
\end{block>

\begin{alertblock>{训练资源需求}
    \begin{itemize>
        \item[\faDesktop] 计算资源：大规模GPU集群
        \item[\faChartBar] 数据资源：TB级高质量文本数据
        \item[\faClock] 时间资源：数月的训练时间
        \item[\faDollarSign] 经济成本：数百万到数千万美元
    \end{itemize>
\end{alertblock>

\begin{block>{训练挑战}
    \begin{itemize>
        \item[\faChartLine] 规模挑战：如何高效训练大规模模型
        \item[\faChartBar] 数据质量：如何确保训练数据的质量
        \item[\faTarget] 目标对齐：如何让模型符合人类期望
        \item[\faGavel] 伦理考虑：如何避免有害内容的学习
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Pre-training Stage
\begin{frame>
\frametitle{预训练：奠定语言理解的基础}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block}{预训练的核心思想}
    \begin{itemize>
        \item[\faBook] \textbf{自监督学习：}从无标注数据中学习
        \item[\faCrystalBall] \textbf{语言建模：}预测下一个词的任务
        \item[\faGlobe] \textbf{通用知识：}学习广泛的语言知识和世界知识
        \item[\faBuilding] \textbf{基础能力：}为后续任务奠定基础
    \end{itemize>
\end{block>

\begin{block}{预训练数据来源}
    \begin{itemize>
        \item[\faGlobe] \textbf{网页文本：}Common Crawl等大规模网页数据
        \item[\faBook] \textbf{书籍语料：}Project Gutenberg等数字图书
        \item[\faNewspaper] \textbf{新闻文章：}各大新闻网站的文章
        \item[\faBookOpen] \textbf{百科全书：}Wikipedia等知识性文本
        \item[\faComments] \textbf{论坛讨论：}Reddit等社交平台内容
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{语言建模任务}
\textbf{输入：}The cat sat on the [MASK]\\
\textbf{目标：}预测下一个词是 "mat"\\
\textbf{损失：}交叉熵损失，衡量预测准确性
\end{block>

\begin{block>{数据预处理}
    \begin{itemize>
        \item[\faBroom] \textbf{清洗过滤：}去除低质量和有害内容
        \item[\faRefresh] \textbf{去重处理：}避免重复内容的影响
        \item[\faChartBar] \textbf{格式统一：}统一文本格式和编码
        \item[\faTarget] \textbf{质量筛选：}基于启发式规则筛选高质量文本
        \item[\faGlobe] \textbf{多语言处理：}处理不同语言的文本
    \end{itemize>
\end{block>

\begin{alertblock>{预训练的效果}
    \begin{itemize>
        \item[\faBrain] 语言理解：学会语法、语义、语用知识
        \item[\faGlobe] 世界知识：获得广泛的事实性知识
        \item[\faLightbulb] 推理能力：发展基本的逻辑推理能力
        \item[\faPalette] 创造能力：具备一定的创意生成能力
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% Fine-tuning Techniques
\begin{frame>
\frametitle{微调：让通用模型适应特定任务}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block}{微调的必要性}
    \begin{itemize>
        \item[\faTarget] \textbf{任务适配：}预训练模型需要适应具体任务
        \item[\faChartLine] \textbf{性能提升：}在特定任务上获得更好性能
        \item[\faWrench] \textbf{行为调整：}调整模型的输出风格和格式
        \item[\faBriefcase] \textbf{商业应用：}满足实际应用的具体需求
    \end{itemize>
\end{block>

\begin{block}{微调的类型}
    \textbf{1. 全参数微调（Full Fine-tuning）}
    \begin{itemize>
        \item[\faRefresh] 更新所有参数：调整模型的所有权重
        \item[\faChartLine] 效果最好：通常能获得最佳性能
        \item[\faDesktop] 资源需求高：需要大量计算资源
        \item[\faClock] 时间较长：训练时间相对较长
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
    \textbf{2. 参数高效微调（PEFT）}
    \begin{itemize>
        \item[\faTarget] 只更新部分参数：冻结大部分预训练参数
        \item[\faBolt] 效率更高：大幅减少计算和存储需求
        \item[\faChartBar] 效果接近：在很多任务上效果接近全参数微调
        \item[\faWrench] 方法多样：LoRA、Adapter、Prompt Tuning等
    \end{itemize>
\end{block>

\begin{block>{主流PEFT方法}
    \textbf{LoRA（Low-Rank Adaptation）：}
    \begin{itemize>
        \item 原理：在原参数矩阵旁添加低秩矩阵
        \item 优势：参数量少，效果好
        \item 应用：广泛用于大模型微调
    \end{itemize>
    
    \textbf{Adapter：}
    \begin{itemize>
        \item 原理：在模型层间插入小型神经网络
        \item 优势：模块化设计，易于管理
        \item 应用：多任务学习场景
    \end{itemize>
\end{block>

\begin{alertblock>{微调策略}
    \begin{itemize}
        \item[\faChartLine] 学习率调整：使用较小的学习率避免灾难性遗忘
        \item[\faTarget] 梯度裁剪：防止梯度爆炸
        \item[\faChartBar] 早停策略：避免过拟合
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% Instruction Tuning
\begin{frame>
\frametitle{指令微调：让AI理解和遵循指令}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block}{指令微调的概念}
    \begin{itemize>
        \item[\faComments] \textbf{指令理解：}训练模型理解和遵循自然语言指令
        \item[\faTarget] \textbf{任务泛化：}一个模型处理多种不同任务
        \item[\faEdit] \textbf{格式统一：}将各种任务统一为指令-回答格式
        \item[\faRobot] \textbf{助手行为：}让模型表现得像智能助手
    \end{itemize>
\end{block>

\begin{block>{指令数据格式}
\textbf{指令：}请为以下新闻写一个标题\\
\textbf{输入：}[新闻正文内容]\\
\textbf{输出：}[生成的新闻标题]

\textbf{指令：}分析这段文字的情感倾向\\
\textbf{输入：}[待分析文本]\\
\textbf{输出：}[情感分析结果]
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{指令数据的构建}
    \begin{itemize>
        \item[\faBook] \textbf{任务多样化：}涵盖各种NLP任务
        \item[\faGlobe] \textbf{领域覆盖：}包含不同领域的任务
        \item[\faComments] \textbf{指令多样化：}同一任务用不同方式表达
        \item[\faChartBar] \textbf{质量控制：}确保指令和回答的质量
    \end{itemize>
\end{block>

\begin{block>{指令微调的优势}
    \begin{itemize>
        \item[\faTarget] \textbf{零样本能力：}能够处理训练时未见过的任务
        \item[\faComments] \textbf{自然交互：}支持自然语言的任务描述
        \item[\faRefresh] \textbf{灵活适应：}容易适应新的任务需求
        \item[\faChartLine] \textbf{性能提升：}在多个任务上表现更好
    \end{itemize>
\end{block>

\begin{alertblock>{在传媒中的应用}
    \begin{itemize>
        \item[\faNewspaper] 内容创作：根据指令生成各种类型的内容
        \item[\faSearch] 信息提取：按指令从文本中提取特定信息
        \item[\faChartBar] 数据分析：根据指令分析文本数据
        \item[\faTarget] 个性化服务：根据用户指令提供定制化服务
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% RLHF
\begin{frame>
\frametitle{RLHF：让AI与人类价值观对齐}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block}{RLHF的动机}
    \begin{itemize>
        \item[\faGavel] \textbf{价值对齐：}让AI的行为符合人类价值观
        \item[\faTarget] \textbf{质量提升：}提高生成内容的质量
        \item[\faShield] \textbf{安全考虑：}减少有害或不当内容的生成
        \item[\faComments] \textbf{用户体验：}提供更符合用户期望的回答
    \end{itemize>
\end{block>

\begin{block>{RLHF的三个阶段}
    \textbf{1. 收集人类偏好数据}
    \begin{itemize>
        \item[\faUsers] 标注员：训练有素的人类标注员
        \item[\faChartBar] 比较任务：对模型输出进行两两比较
        \item[\faTarget] 偏好标注：标注哪个回答更好
        \item[\faChartLine] 数据规模：通常需要数万到数十万条比较
    \end{itemize>
    
    \textbf{2. 训练奖励模型}
    \begin{itemize>
        \item[\faTarget] 目标：学习人类的偏好模式
        \item[\faChartBar] 输入：模型输出文本
        \item[\faChartLine] 输出：质量评分
        \item[\faWrench] 架构：通常基于预训练模型
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
    \textbf{3. 强化学习优化}
    \begin{itemize>
        \item[\faGamepad] 强化学习：使用PPO等算法优化
        \item[\faTrophy] 奖励信号：来自训练好的奖励模型
        \item[\faTarget] 优化目标：最大化奖励模型的评分
        \item[\faGavel] 平衡考虑：避免过度优化导致的问题
    \end{itemize>
\end{block>

\begin{block>{RLHF的效果}
    \begin{itemize>
        \item[\faCheck] \textbf{有用性：}回答更有帮助和相关
        \item[\faShield] \textbf{无害性：}减少有害内容的生成
        \item[\faHundredPoints] \textbf{诚实性：}减少虚假信息的产生
        \item[\faComments] \textbf{对话质量：}提高对话的自然度和连贯性
    \end{itemize}
\end{block>

\begin{alertblock}{挑战与限制}
    \begin{itemize}
        \item[\faDollarSign] 成本高昂：需要大量人工标注
        \item[\faTarget] 主观性：人类偏好存在主观性和不一致
        \item[\faGlobe] 文化差异：不同文化背景的偏好差异
        \item[\faChartBar] 规模限制：难以覆盖所有可能的场景
    \end{itemize>
\end{alertblock>

\begin{block>{在传媒中的意义}
    \begin{itemize}
        \item[\faNewspaper] 内容质量：确保生成内容的质量和准确性
        \item[\faGavel] 伦理合规：符合新闻伦理和社会价值观
        \item[\faTarget] 用户满意度：提供更符合用户期望的内容
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Training Data Importance
\begin{frame>
\frametitle{数据为王：训练数据决定模型质量}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{数据质量的重要性}
    \begin{itemize>
        \item[\faTarget] \textbf{决定上限：}数据质量决定模型性能上限
        \item[\faBrain] \textbf{影响理解：}数据内容影响模型的知识结构
        \item[\faGavel] \textbf{价值观传递：}数据中的偏见会被模型学习
        \item[\faGlobe] \textbf{能力边界：}数据覆盖范围决定模型能力边界
    \end{itemize>
\end{block>

\begin{block>{高质量数据的特征}
    \begin{itemize>
        \item[\faCheck] \textbf{准确性：}信息准确，事实正确
        \item[\faGlobe] \textbf{多样性：}涵盖不同领域、风格、观点
        \item[\faChartBar] \textbf{代表性：}能够代表真实世界的分布
        \item[\faRefresh] \textbf{时效性：}包含最新的信息和知识
        \item[\faTarget] \textbf{相关性：}与目标任务和应用场景相关
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block>{数据来源与挑战}
    \textbf{网络数据：}
    \begin{itemize}
        \item 优势：规模大，覆盖面广
        \item 挑战：质量参差不齐，存在噪声
    \end{itemize>
    
    \textbf{专业文献：}
    \begin{itemize}
        \item 优势：质量高，权威性强
        \item 挑战：获取困难，版权限制
    \end{itemize>
    
    \textbf{用户生成内容：}
    \begin{itemize}
        \item 优势：真实性强，语言自然
        \item 挑战：质量不稳定，隐私问题
    \end{itemize>
\end{block>

\begin{block>{数据预处理流程}
    \begin{enumerate>
        \item \textbf{收集：}从各种来源收集原始数据
        \item \textbf{清洗：}去除格式错误、编码问题等
        \item \textbf{过滤：}基于质量标准筛选数据
        \item \textbf{去重：}移除重复或近似重复的内容
        \item \textbf{标准化：}统一格式和编码标准
        \item \textbf{验证：}人工或自动验证数据质量
    \end{enumerate>
\end{block>

\begin{alertblock>{在传媒训练中的考虑}
    \begin{itemize}
        \item[\faNewspaper] 新闻质量：确保训练数据中新闻的准确性
        \item[\faGlobe] 多元观点：包含不同立场和观点的内容
        \item[\faGavel] 伦理标准：符合新闻伦理和职业标准
        \item[\faTarget] 领域覆盖：涵盖传媒工作的各个方面
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% Scaling Laws
\begin{frame>
\frametitle{规模定律：更大的模型，更强的能力}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block}{规模定律（Scaling Laws）}
    \begin{itemize>
        \item[\faChartLine] \textbf{参数量增长：}模型性能随参数量增长而提升
        \item[\faChartBar] \textbf{数据量影响：}更多数据带来更好性能
        \item[\faBolt] \textbf{计算量关系：}计算量与性能呈幂律关系
        \item[\faTarget] \textbf{可预测性：}性能提升在一定程度上可预测
    \end{itemize>
\end{block>

\begin{block>{参数量的演进}
\textbf{GPT-1 (2018):} 117M参数\\
\textbf{GPT-2 (2019):} 1.5B参数\\
\textbf{GPT-3 (2020):} 175B参数\\
\textbf{PaLM (2022):} 540B参数\\
\textbf{GPT-4 (2023):} 估计1.7T参数
\end{block>

\begin{block>{涌现能力（Emergent Abilities）}
    \begin{itemize>
        \item[\faLightbulb] \textbf{定义：}在某个规模阈值后突然出现的新能力
        \item[\faTarget] \textbf{特征：}不能从小规模模型的性能预测
        \item[\faChartLine] \textbf{例子：}少样本学习、复杂推理、代码生成、多语言理解
    \end{itemize}
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block}{规模带来的改进}
    \begin{itemize>
        \item[\faBrain] \textbf{理解能力：}更深入的语言理解
        \item[\faLightbulb] \textbf{推理能力：}更强的逻辑推理
        \item[\faPalette] \textbf{创造能力：}更好的创意生成
        \item[\faGlobe] \textbf{知识覆盖：}更广泛的知识掌握
        \item[\faTarget] \textbf{任务泛化：}更好的零样本和少样本性能
    \end{itemize>
\end{block>

\begin{alertblock>{规模的挑战}
    \begin{itemize>
        \item[\faDollarSign] 训练成本：指数级增长的计算成本
        \item[\faClock] 训练时间：更长的训练周期
        \item[\faDesktop] 硬件需求：对计算基础设施的高要求
        \item[\faWrench] 工程复杂性：分布式训练的技术挑战
        \item[\faGlobe] 环境影响：大量能源消耗的环境问题
    \end{itemize>
\end{alertblock>

\begin{block>{效率优化方向}
    \begin{itemize>
        \item[\faBuilding] 架构创新：设计更高效的模型架构
        \item[\faWrench] 训练技术：改进训练算法和技术
        \item[\faChartBar] 数据效率：提高数据利用效率
        \item[\faBolt] 推理优化：减少推理时的计算需求
        \item[\faTarget] 专门化：针对特定任务优化模型
    \end{itemize>
\end{block>

\begin{block}{对传媒行业的启示}
    \begin{itemize>
        \item[\faTarget] 能力预期：更大的模型将带来更强的传媒应用能力
        \item[\faDollarSign] 成本考虑：需要平衡性能需求和成本投入
        \item[\faRefresh] 技术跟进：关注最新的大模型发展
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% Training Technical Challenges
\begin{frame>
\frametitle{训练大模型：技术挑战与解决方案}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{alertblock>{计算挑战}
    \begin{itemize>
        \item[\faDesktop] 硬件需求：需要大规模GPU集群
        \item[\faBolt] 内存限制：模型参数超出单卡内存
        \item[\faRefresh] 通信瓶颈：多卡间的数据传输延迟
        \item[\faChartBar] 负载均衡：计算任务的合理分配
    \end{itemize>
\end{alertblock>

\begin{block>{分布式训练技术}
    \begin{itemize>
        \item[\faChartBar] \textbf{数据并行：}将数据分布到多个设备
        \item[\faBrain] \textbf{模型并行：}将模型分布到多个设备
        \item[\faRefresh] \textbf{流水线并行：}将计算过程流水线化
        \item[\faTarget] \textbf{混合并行：}结合多种并行策略
    \end{itemize>
\end{block>

\begin{block>{内存优化技术}
    \begin{itemize}
        \item[\faRefresh] 梯度检查点：重计算中间结果节省内存
        \item[\faChartBar] 混合精度训练：使用FP16减少内存使用
        \item[\faTarget] ZeRO优化器：分布式优化器状态
        \item[\faSave] CPU卸载：将部分计算卸载到CPU
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
\begin{alertblock>{训练稳定性挑战}
    \begin{itemize>
        \item[\faChartUp] 梯度爆炸：梯度值过大导致训练不稳定
        \item[\faChartDown] 梯度消失：梯度值过小导致学习缓慢
        \item[\faTarget] 数值不稳定：浮点运算的精度问题
        \item[\faRefresh] 收敛困难：大模型的收敛更加困难
    \end{itemize>
\end{alertblock>

\begin{block>{稳定性解决方案}
    \begin{itemize>
        \item[\faCut] 梯度裁剪：限制梯度的最大值
        \item[\faChartBar] 学习率调度：动态调整学习率
        \item[\faTarget] 权重初始化：合理的参数初始化策略
        \item[\faRefresh] 批归一化：稳定训练过程
    \end{itemize>
\end{block>

\begin{block>{监控与调试}
    \begin{itemize>
        \item[\faChartLine] 训练监控：实时监控训练指标
        \item[\faSearch] 性能分析：分析训练瓶颈
        \item[\faBug] 错误诊断：快速定位和解决问题
        \item[\faChartBar] 资源监控：监控硬件资源使用
    \end{itemize>
\end{block>

\begin{block>{工程最佳实践}
    \begin{itemize>
        \item[\faRefresh] 检查点保存：定期保存模型状态
        \item[\faEdit] 实验记录：详细记录实验配置和结果
        \item[\faMicroscope] 小规模验证：先在小规模上验证方案
        \item[\faTarget] 渐进式扩展：逐步扩大训练规模
    \end{itemize}
\end{block>
\end{column>
\end{columns>
\end{frame>

% Section: Capability Boundaries
\section{能力边界分析}

% LLM Core Advantages
\begin{frame>
\frametitle{LLM的超能力：理解、生成、推理}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block>{强大的语言理解能力}
    \begin{itemize>
        \item[\faBrain] \textbf{深度理解：}理解文本的深层含义和隐含信息
        \item[\faGlobe] \textbf{上下文感知：}基于长上下文进行准确理解
        \item[\faLightbulb] \textbf{语义推理：}进行复杂的语义推理和关联
        \item[\faTarget] \textbf{意图识别：}准确识别用户的真实意图
    \end{itemize>
\end{block>

\begin{block>{卓越的内容生成能力}
    \begin{itemize>
        \item[\faEdit] \textbf{多样化写作：}适应不同风格、体裁、受众
        \item[\faPalette] \textbf{创意生成：}产生新颖的想法和创意内容
        \item[\faRefresh] \textbf{格式适配：}生成各种格式和结构的内容
        \item[\faChartBar] \textbf{结构化输出：}按要求组织信息结构
    \end{itemize}
\end{block>

\begin{block>{出色的推理能力}
    \begin{itemize}
        \item[\faCalculator] \textbf{逻辑推理：}进行复杂的逻辑分析和推理
        \item[\faLink] \textbf{因果关系：}理解和分析因果关系
        \item[\faChartBar] \textbf{数据分析：}从数据中提取洞察和结论
        \item[\faTarget] \textbf{问题解决：}分解和解决复杂问题
    \end{itemize}
\end{block>
\end{column}
\begin{column>{0.5\textwidth>
\begin{block>{广泛的知识覆盖}
    \begin{itemize>
        \item[\faGlobe] \textbf{百科知识：}涵盖各个领域的基础知识
        \item[\faBook] \textbf{专业知识：}在多个专业领域有深入了解
        \item[\faGlobe] \textbf{多语言能力：}支持多种语言的理解和生成
        \item[\faClock] \textbf{时事了解：}掌握训练数据截止时间前的信息
    \end{itemize>
\end{block>

\begin{block>{灵活的学习适应能力}
    \begin{itemize}
        \item[\faRocket] \textbf{快速学习：}通过少量示例快速适应新任务
        \item[\faRefresh] \textbf{上下文学习：}在对话中学习和适应
        \item[\faTarget] \textbf{个性化：}根据用户偏好调整输出风格
        \item[\faGlobe] \textbf{跨领域迁移：}在不同领域间迁移知识
    \end{itemize>
\end{block>

\begin{alertblock>{在传媒中的优势体现}
    \begin{itemize>
        \item[\faNewspaper] 内容创作：高质量的新闻、文章、脚本创作
        \item[\faSearch] 信息分析：深度的文本分析和信息提取
        \item[\faComments] 用户交互：自然流畅的用户对话和服务
        \item[\faTarget] 个性化服务：基于用户需求的定制化内容
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% LLM Limitations and Hallucinations
\begin{frame>
\frametitle{理性认识：LLM的局限性与挑战}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{alertblock}{"幻觉"现象详解}
    \textbf{定义：}模型生成看似合理但实际错误的信息
    
    \textbf{表现形式：}
    \begin{itemize>
        \item 编造不存在的事实
        \item 虚构人名、地名、数据
        \item 创造虚假的引用和来源
        \item 生成错误的数学计算
    \end{itemize>
    
    \textbf{产生原因：}
    \begin{itemize>
        \item[\faBrain] 基于模式匹配而非真实理解
        \item[\faChartBar] 训练数据中的错误信息
        \item[\faTarget] 被要求生成回答时的"创造"倾向
        \item[\faRefresh] 基于概率的生成机制
    \end{itemize>
\end{alertblock>
\end{column>
\begin{column>{0.5\textwidth>
\begin{alertblock>{其他主要局限性}
    \begin{itemize>
        \item[\faCalendar] \textbf{知识截止：}无法获取训练后的最新信息
        \item[\faCalculator] \textbf{数学计算：}在复杂数学运算上的不足
        \item[\faSearch] \textbf{事实核查：}无法实时验证信息的准确性
        \item[\faSave] \textbf{记忆限制：}上下文长度的物理限制
        \item[\faTarget] \textbf{一致性问题：}同样问题可能得到不同答案
    \end{itemize>
\end{alertblock>

\begin{alertblock>{在传媒应用中的风险}
    \begin{itemize>
        \item[\faNewspaper] 新闻准确性：可能生成虚假新闻信息
        \item[\faChartBar] 数据错误：统计数据和事实的错误
        \item[\faUsers] 人物信息：关于真实人物的错误信息
        \item[\faGlobe] 事件描述：对历史事件的错误描述
    \end{itemize>
\end{alertblock>

\begin{block>{应对策略}
    \begin{itemize>
        \item[\faCheck] 人工审核：建立严格的人工审核机制
        \item[\faSearch] 多源验证：交叉验证重要信息
        \item[\faBook] 知识库辅助：结合可靠的知识库
        \item[\faTarget] 置信度评估：评估生成内容的可信度
        \item[\faExclamationTriangle] 风险提示：向用户明确AI的局限性
    \end{itemize>
\end{block>
\end{column>
\end{columns>
\end{frame>

% How to Use LLM Correctly
\begin{frame>
\frametitle{善用AI：发挥优势，规避风险}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block}{使用原则}
    \begin{itemize>
        \item[\faTarget] \textbf{明确定位：}将LLM作为辅助工具而非替代品
        \item[\faCheck] \textbf{验证为先：}对重要信息进行独立验证
        \item[\faRefresh] \textbf{迭代改进：}通过反馈不断优化使用效果
        \item[\faGavel] \textbf{伦理考虑：}确保使用符合伦理和法律要求
    \end{itemize>
\end{block>

\begin{block}{适合的应用场景}
    \begin{itemize>
        \item[\faLightbulb] \textbf{创意启发：}头脑风暴和创意生成
        \item[\faEdit] \textbf{内容草稿：}初稿写作和内容框架
        \item[\faSearch] \textbf{信息整理：}文本摘要和信息提取
        \item[\faTarget] \textbf{格式转换：}内容的格式化和重组
        \item[\faComments] \textbf{对话交互：}用户咨询和客服支持
    \end{itemize>
\end{block>

\begin{alertblock>{需要谨慎的场景}
    \begin{itemize>
        \item[\faChartBar] 事实性信息：具体的数据、日期、人名等
        \item[\faGavel] 法律建议：法律相关的专业建议
        \item[\faHospital] 医疗信息：健康和医疗相关信息
        \item[\faDollarSign] 金融建议：投资和财务相关建议
        \item[\faMicroscope] 科学计算：精确的数学和科学计算
    \end{itemize>
\end{alertblock>
\end{column>
\begin{column>{0.5\textwidth>
\begin{block}{质量控制策略}
    \begin{itemize>
        \item[\faUsers] \textbf{人机协作：}结合人类专业知识和AI能力
        \item[\faSearch] \textbf{多重检查：}建立多层次的质量检查机制
        \item[\faChartBar] \textbf{基准测试：}定期测试AI的性能表现
        \item[\faEdit] \textbf{文档记录：}记录AI使用的过程和结果
        \item[\faRefresh] \textbf{持续监控：}持续监控AI输出的质量
    \end{itemize>
\end{block>

\begin{block}{在传媒工作中的最佳实践}
    \textbf{新闻写作：}
    \begin{itemize}
        \item[\faCheck] 用于生成文章框架和初稿
        \item[\faTimes] 不直接用于事实性报道
        \item[\faSearch] 必须进行事实核查和编辑审核
    \end{itemize>
    
    \textbf{创意策划：}
    \begin{itemize}
        \item[\faCheck] 用于创意头脑风暴
        \item[\faCheck] 生成多种创意方案
        \item[\faTarget] 结合专业判断选择最佳方案
    \end{itemize}
    
    \textbf{数据分析：}
    \begin{itemize>
        \item[\faCheck] 用于数据解读和洞察生成
        \item[\faTimes] 不用于精确的数值计算
        \item[\faSearch] 需要验证分析结论的准确性
    \end{itemize>
\end{block>

\begin{alertblock>{用户教育要点}
    \begin{itemize>
        \item[\faBrain] 理解原理：了解AI的工作原理和局限性
        \item[\faTarget] 合理期望：设定合理的使用期望
        \item[\faWrench] 技能培养：培养有效使用AI的技能
        \item[\faGavel] 责任意识：明确使用AI的责任和边界
    \end{itemize>
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

% Course Summary and Next Week Preview
\begin{frame>
\frametitle{第2周总结：深入理解LLM的技术基础}
\begin{columns>
\begin{column>{0.5\textwidth>
\begin{block}{本周重点回顾}
    \textbf{Transformer架构：}
    \begin{itemize}
        \item 注意力机制的工作原理
        \item 多头注意力的优势
        \item 位置编码的重要性
        \item 架构的革命性影响
    \end{itemize}
    
    \textbf{Tokenization技术：}
    \begin{itemize>
        \item Token的概念和重要性
        \item 不同分词方法的对比
        \item BPE算法的原理
        \item 分词对模型理解的影响
    \end{itemize>
\end{block>

\begin{block}{关键概念掌握}
    \begin{itemize>
        \item[\faTarget] \textbf{Self-Attention：}序列内部关系建模的核心机制
        \item[\faRefresh] \textbf{预训练+微调：}现代LLM的标准训练范式
        \item[\faComments] \textbf{指令微调：}让AI理解和遵循自然语言指令
        \item[\faTrophy] \textbf{RLHF：}让AI与人类价值观对齐的关键技术
        \item[\faExclamationTriangle] \textbf{幻觉现象：}AI生成错误信息的重要风险
    \end{itemize>
\end{block>
\end{column>
\begin{column>{0.5\textwidth>
    \textbf{LLM训练过程：}
    \begin{itemize}
        \item 预训练的核心思想
        \item 微调技术的应用
        \item 指令微调的价值
        \item RLHF的重要作用
    \end{itemize>
    
    \textbf{能力边界认知：}
    \begin{itemize}
        \item LLM的核心优势
        \item 局限性和"幻觉"现象
        \item 正确使用的原则
        \item 质量控制策略
    \end{itemize>
\end{block>

\begin{block}{实际应用启示}
    \begin{itemize>
        \item[\faWrench] 工具定位：将LLM作为强大的辅助工具
        \item[\faCheck] 验证机制：建立严格的内容验证流程
        \item[\faTarget] 场景选择：在合适的场景中发挥LLM优势
        \item[\faGavel] 风险控制：充分认识和控制使用风险
    \end{itemize}
\end{block>

\begin{alertblock}{下周预告：第3周 - 提示词工程基础}
    \textbf{学习目标：}掌握与AI有效沟通的艺术
    
    \textbf{主要内容：}
    \begin{itemize>
        \item 提示词的核心地位和重要性
        \item CRISPE框架的详细应用
        \item 四种基础提示模式
        \item 常见错误分析和改进方法
    \end{itemize>
    
    \textbf{实践重点：}设计有效的提示词，避免常见错误，针对传媒场景的提示词优化
\end{alertblock>
\end{column>
\end{columns>
\end{frame>

\end{document>