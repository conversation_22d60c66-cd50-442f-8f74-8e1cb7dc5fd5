% Week 2: LLM Principles & Technical Foundations
\input{ai_course_template}

\title{第2周：LLM工作原理与技术基础}
\subtitle{LLM Principles \& Technical Foundations}
\author{课程教师}
\institute{长江新闻与传播学院 \\ 汕头大学}
\date{第2周课程}

\begin{document}

% Title slide
\begin{frame}[plain]
    \titlepage
\end{frame}

% Table of contents
\begin{frame}{课程概览}
    \tableofcontents
\end{frame}

\section{第1部分：回顾与导入}

\begin{frame}{第1周内容回顾}
    \framesubtitle{Week 1 Review}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{主要内容回顾}
                \begin{itemize}
                    \item \textbf{课程目标}：掌握AI大模型在传媒领域的应用
                    \item \textbf{AI发展简史}：从达特茅斯会议到大模型时代
                    \item \textbf{机器学习基础}：三种学习类型
                    \item \textbf{LLM概述}：大语言模型的定义和特征
                    \item \textbf{传媒应用}：AI在传媒各环节的应用潜力
                \end{itemize}
            \end{block}
        \end{column}
        
        \begin{column}{0.5\textwidth}
            \begin{block}{关键概念}
                \begin{itemize}
                    \item 人工智能的发展历程
                    \item 机器学习的三种类型
                    \item 深度学习与神经网络
                    \item 大语言模型的突破性意义
                    \item 数据驱动的学习范式
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{思考问题}
                \begin{itemize}
                    \item AI技术如何改变传媒行业？
                    \item 大语言模型与传统AI有什么区别？
                    \item 传媒人如何适应AI时代？
                \end{itemize>
            \end{exampleblock>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{第2周学习目标与内容预览}
    \framesubtitle{Week 2 Learning Objectives}
    
    \begin{block}{学习目标}
        \begin{itemize}
            \item \highlight{了解Transformer架构}的基本原理和注意力机制
            \item \highlight{理解Tokenization概念}及其对模型的影响
            \item \highlight{掌握LLM的训练过程}：预训练、微调、RLHF
            \item \highlight{认识LLM的能力边界}与局限性
        \end{itemize}
    \end{block}
    
    \vspace{0.3cm}
    
    \begin{columns}[t]
        \begin{column}{0.24\textwidth}
            \begin{block}{\small Transformer架构}
                \tiny
                \begin{itemize}
                    \item 注意力机制直观理解
                    \item Self-Attention工作原理
                    \item Encoder-Decoder结构
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column}{0.24\textwidth}
            \begin{block}{\small Tokenization}
                \tiny
                \begin{itemize}
                    \item 什么是Token
                    \item 分词方法对比
                    \item Token对模型理解的影响
                \end{itemize}
            \end{block>
        \end{column}
        
        \begin{column}{0.24\textwidth}
            \begin{block}{\small LLM训练过程}
                \tiny
                \begin{itemize}
                    \item 预训练阶段详解
                    \item 微调技术介绍
                    \item RLHF人类反馈强化学习
                \end{itemize>
            \end{block>
        \end{column}
        
        \begin{column}{0.24\textwidth}
            \begin{block}{\small 能力边界分析}
                \tiny
                \begin{itemize}
                    \item LLM的优势与局限
                    \item "幻觉"现象解析
                    \item 实际应用考虑
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns}
\end{frame>

\section{第2部分：Transformer架构}

\begin{frame}{从RNN到Transformer：架构演进的必然}
    \framesubtitle{From RNN to Transformer: Inevitable Evolution}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{alertblock}{传统RNN的局限性}
                \begin{itemize}
                    \item \warning{序列处理}：必须按顺序处理，无法并行
                    \item \warning{长距离依赖}：难以捕捉长序列中的远距离关系
                    \item \warning{梯度问题}：梯度消失和梯度爆炸
                    \item \warning{训练效率}：训练时间长，计算效率低
                \end{itemize>
            \end{alertblock>
            
            \vspace{0.3cm}
            
            \begin{block}{LSTM的改进与不足}
                \textbf{改进}：通过门控机制缓解梯度问题 \\
                \textbf{问题}：序列处理限制依然存在，结构复杂
            \end{block>
        \end{column}
        
        \begin{column}{0.5\textwidth}
            \begin{exampleblock}{Transformer的突破}
                \begin{itemize}
                    \item \success{并行处理}：所有位置可以同时计算
                    \item \success{长距离依赖}：直接建模任意位置间关系
                    \item \success{训练效率}：大幅提升训练速度
                    \item \success{简洁优雅}：架构相对简单，易于理解
                \end{itemize>
            \end{exampleblock>
            
            \vspace{0.3cm}
            
            \begin{block}{影响深远}
                \begin{itemize}
                    \item 在多个NLP任务上取得突破
                    \item 成为现代大模型的基础架构
                    \item 从NLP扩展到计算机视觉等领域
                \end{itemize}
            \end{block>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{注意力机制：模拟人类的注意力}
    \framesubtitle{Attention Mechanism: Simulating Human Attention}
    
    \begin{columns}
        \begin{column}{0.6\textwidth}
            \begin{block}{人类注意力的特点}
                \begin{itemize}
                    \item \textbf{选择性关注}：在复杂环境中聚焦重要信息
                    \item \textbf{动态调整}：根据任务需求调整注意力分配
                    \item \textbf{并行处理}：同时处理多个信息源
                    \item \textbf{上下文相关}：基于上下文决定关注重点
                \end{itemize}
            \end{block}
            
            \vspace{0.3cm}
            
            \begin{block}{机器注意力机制}
                \begin{itemize}
                    \item \textbf{权重分配}：为不同位置分配不同权重
                    \item \textbf{相关性计算}：计算查询与键之间的相关性
                    \item \textbf{加权求和}：基于权重对值进行加权平均
                    \item \textbf{动态聚焦}：根据输入动态调整关注重点
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column}{0.4\textwidth}
            \begin{center}
                \begin{tikzpicture}[scale=0.7]
                    % Attention visualization
                    \foreach \i in {1,2,3,4,5} {
                        \node[circle,draw,fill=AILightBlue,minimum size=0.6cm] (word\i) at (\i,3) {\tiny W\i};
                    }
                    
                    % Current focus word
                    \node[circle,draw,fill=AIGreen,minimum size=0.6cm] (focus) at (3,1.5) {\tiny W3};
                    
                    % Attention weights (represented by line thickness)
                    \draw[thick,AIBlue] (focus) -- (word1);
                    \draw[very thick,AIGreen] (focus) -- (word2);
                    \draw[ultra thick,AIRed] (focus) -- (word3);
                    \draw[thick,AIGreen] (focus) -- (word4);
                    \draw[thin,AIGray] (focus) -- (word5);
                    
                    \node[below] at (3,0.8) {\tiny 当前词};
                    \node[above] at (3,3.5) {\tiny 上下文词};
                    
                    % Attention weights
                    \node[right] at (5.5,3) {\tiny 0.1};
                    \node[right] at (5.5,2.6) {\tiny 0.3};
                    \node[right] at (5.5,2.2) {\tiny 0.4};
                    \node[right] at (5.5,1.8) {\tiny 0.15};
                    \node[right] at (5.5,1.4) {\tiny 0.05};
                \end{tikzpicture}
            \end{center>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{生活中的类比}
                \begin{itemize}
                    \item 阅读时关注关键词
                    \item 听音乐时聚焦主旋律
                    \item 开车时关注路况信号
                    \item 对话中专注于说话者
                \end{itemize>
            \end{exampleblock>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{Self-Attention机制详解}
    \framesubtitle{Self-Attention Mechanism Explained}
    
    \begin{columns}
        \begin{column}{0.6\textwidth}
            \begin{block}{Self-Attention的核心思想}
                \begin{itemize}
                    \item \textbf{自我关注}：序列中每个位置关注其他所有位置
                    \item \textbf{全局连接}：直接建模任意两个位置间的关系
                    \item \textbf{权重计算}：动态计算每个位置的重要性权重
                    \item \textbf{信息整合}：基于权重整合全局信息
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{三个关键概念：Query、Key、Value}
                \begin{itemize}
                    \item \textbf{Query（查询）}：当前位置想要查找的信息
                    \item \textbf{Key（键）}：每个位置提供的索引信息
                    \item \textbf{Value（值）}：每个位置包含的实际内容
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{数学公式}
                \[
                \text{Attention}(Q,K,V) = \text{Softmax}\left(\frac{QK^T}{\sqrt{d_k}}\right)V
                \]
            \end{exampleblock>
        \end{column}
        
        \begin{column}{0.4\textwidth}
            \begin{center}
                \begin{tikzpicture}[scale=0.6]
                    % Self-attention computation steps
                    
                    % Input sequence
                    \foreach \i/\word in {1/The,2/cat,3/sat,4/on,5/mat} {
                        \node[rectangle,draw,fill=AILightBlue] (input\i) at (\i,4) {\tiny \word};
                    }
                    
                    % Q, K, V transformation
                    \foreach \i in {1,2,3,4,5} {
                        \node[rectangle,draw,fill=AIGreen] (q\i) at (\i,3) {\tiny Q};
                        \node[rectangle,draw,fill=AIOrange] (k\i) at (\i,2.3) {\tiny K};
                        \node[rectangle,draw,fill=AIRed] (v\i) at (\i,1.6) {\tiny V};
                        
                        \draw[->] (input\i) -- (q\i);
                        \draw[->] (input\i) -- (k\i);
                        \draw[->] (input\i) -- (v\i);
                    }
                    
                    % Attention computation
                    \node[rectangle,draw,fill=AIBlue] (attention) at (3,0.5) {\tiny Attention};
                    
                    \foreach \i in {1,2,3,4,5} {
                        \draw[->] (q\i) -- (attention);
                        \draw[->] (k\i) -- (attention);
                        \draw[->] (v\i) -- (attention);
                    }
                    
                    % Labels
                    \node[left] at (0.5,4) {\tiny 输入};
                    \node[left] at (0.5,3) {\tiny Query};
                    \node[left] at (0.5,2.3) {\tiny Key};
                    \node[left] at (0.5,1.6) {\tiny Value};
                    \node[left] at (0.5,0.5) {\tiny 注意力};
                \end{tikzpicture}
            \end{center>
            
            \vspace{0.3cm}
            
            \begin{block}{计算过程}
                \begin{enumerate}
                    \item \textbf{计算相似度}：$\text{Score} = Q \times K^T$
                    \item \textbf{归一化权重}：$\text{Weight} = \text{Softmax}(\text{Score})$
                    \item \textbf{加权求和}：$\text{Output} = \text{Weight} \times V$
                \end{enumerate}
            \end{block>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{多头注意力机制}
    \framesubtitle{Multi-Head Attention: Multiple Perspectives}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{为什么需要多头注意力？}
                \begin{itemize>
                    \item \textbf{多样化关注}：从不同角度关注信息
                    \item \textbf{丰富表示}：捕捉更丰富的语义关系
                    \item \textbf{并行处理}：多个注意力头并行工作
                    \item \textbf{性能提升}：显著提升模型表现
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{工作原理}
                \begin{enumerate>
                    \item \textbf{线性变换}：输入投影到多个子空间
                    \item \textbf{并行计算}：每个头独立计算注意力
                    \item \textbf{结果拼接}：将所有头的输出拼接
                    \item \textbf{最终投影}：通过线性层得到最终输出
                \end{enumerate>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{数学表示}
                \small
                \begin{align}
                \text{MultiHead}(Q,K,V) &= \text{Concat}(\text{head}_1, \ldots, \text{head}_h)W^O\\
                \text{head}_i &= \text{Attention}(QW_i^Q, KW_i^K, VW_i^V)
                \end{align}
            \end{exampleblock>
        \end{column>
        
        \begin{column>{0.5\textwidth}
            \begin{center}
                \begin{tikzpicture}[scale=0.6]
                    % Multi-head attention visualization
                    
                    % Input
                    \node[rectangle,draw,fill=AILightBlue,minimum width=3cm] (input) at (0,4) {Input Sequence};
                    
                    % Multiple heads
                    \foreach \i/\color in {1/AIGreen,2/AIOrange,3/AIRed,4/AIBlue} {
                        \node[rectangle,draw,fill=\color,minimum width=1.5cm] (head\i) at (\i-2.5,2.5) {\tiny Head \i};
                        \draw[->] (input) -- (head\i);
                    }
                    
                    % Concatenation
                    \node[rectangle,draw,fill=AIGray,minimum width=3cm] (concat) at (0,1) {Concatenate};
                    \foreach \i in {1,2,3,4} {
                        \draw[->] (head\i) -- (concat);
                    }
                    
                    % Output projection
                    \node[rectangle,draw,fill=AIBlue,minimum width=3cm] (output) at (0,-0.5) {Linear Projection};
                    \draw[->] (concat) -- (output);
                    
                    % Labels
                    \node[right] at (3.5,2.5) {\tiny 8个头};
                \end{tikzpicture}
            \end{center>
            
            \vspace{0.3cm}
            
            \begin{block}{不同头的作用}
                \begin{itemize}
                    \item \textbf{语法关系}：某些头专注于语法结构
                    \item \textbf{语义关系}：某些头关注语义相关性
                    \item \textbf{位置关系}：某些头捕捉位置信息
                    \item \textbf{长距离依赖}：某些头建模远距离关系
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{类比理解}
                就像团队协作中不同专家从不同角度分析问题，多头注意力让模型从多个视角理解文本。
            \end{exampleblock>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{位置编码的重要性}
    \framesubtitle{Positional Encoding: Injecting Sequence Information}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{位置信息的重要性}
                \begin{itemize>
                    \item \textbf{序列顺序}：语言中词序对意义至关重要
                    \item \textbf{时间关系}：事件的时间顺序影响理解
                    \item \textbf{结构信息}：句法结构依赖位置关系
                    \item \textbf{语义区分}：相同词在不同位置意义不同
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{alertblock}{Transformer的挑战}
                \begin{itemize>
                    \item Self-Attention机制本身不考虑位置
                    \item 打乱输入顺序结果相同（置换不变性）
                    \item 必须显式添加位置信息
                \end{itemize>
            \end{alertblock>
            
            \vspace{0.3cm}
            
            \begin{block}{设计原则}
                \begin{itemize>
                    \item \textbf{唯一性}：每个位置有唯一编码
                    \item \textbf{相对关系}：表示位置间相对关系
                    \item \textbf{可扩展性}：处理不同长度序列
                    \item \textbf{计算效率}：编码计算要高效
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column}{0.5\textwidth}
            \begin{center>
                \begin{tikzpicture}[scale=0.7]
                    % Positional encoding visualization
                    
                    % Word embeddings
                    \foreach \i/\word in {1/The,2/cat,3/sat} {
                        \node[rectangle,draw,fill=AILightBlue] (word\i) at (\i*2,3) {\word};
                        \node[below] at (\i*2,2.7) {\tiny 词嵌入};
                    }
                    
                    % Positional encodings
                    \foreach \i in {1,2,3} {
                        \node[rectangle,draw,fill=AIGreen] (pos\i) at (\i*2,1.5) {PE\i};
                        \node[below] at (\i*2,1.2) {\tiny 位置编码};
                    }
                    
                    % Addition
                    \foreach \i in {1,2,3} {
                        \node[circle,draw,fill=AIOrange] (add\i) at (\i*2,0) {+};
                        \draw[->] (word\i) -- (add\i);
                        \draw[->] (pos\i) -- (add\i);
                    }
                    
                    % Final embeddings
                    \node[below] at (4,-0.5) {\tiny 最终嵌入表示};
                \end{tikzpicture}
            \end{center>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{正弦位置编码}
                \small
                \begin{align}
                PE_{(pos,2i)} &= \sin\left(\frac{pos}{10000^{2i/d_{model}}}\right)\\
                PE_{(pos,2i+1)} &= \cos\left(\frac{pos}{10000^{2i/d_{model}}}\right)
                \end{align}
            \end{exampleblock>
            
            \vspace{0.3cm}
            
            \begin{block}{正弦编码的优势}
                \begin{itemize>
                    \item 周期性：不同频率的正弦波组合
                    \item 相对位置：能够表示相对位置关系
                    \item 外推能力：处理训练时未见过的长度
                    \item 计算简单：无需学习参数
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
\end{frame}

\begin{frame}{Transformer的完整架构}
    \framesubtitle{Complete Transformer Architecture}
    
    \begin{center}
        \begin{tikzpicture}[scale=0.8]
            % Encoder stack
            \foreach \i in {1,2,3} {
                \draw[thick,AIBlue] (0,\i*2-1) rectangle (3,\i*2+0.5);
                \node at (1.5,\i*2-0.25) {\tiny Encoder Layer \i};
                
                % Multi-head attention
                \draw[fill=AIGreen] (0.2,\i*2-0.8) rectangle (2.8,\i*2-0.4);
                \node at (1.5,\i*2-0.6) {\tiny Multi-Head Attention};
                
                % Feed forward
                \draw[fill=AIOrange] (0.2,\i*2-0.2) rectangle (2.8,\i*2+0.2);
                \node at (1.5,\i*2) {\tiny Feed Forward};
            }
            
            % Decoder stack
            \foreach \i in {1,2,3} {
                \draw[thick,AIRed] (5,\i*2-1) rectangle (8,\i*2+0.5);
                \node at (6.5,\i*2-0.25) {\tiny Decoder Layer \i};
                
                % Masked multi-head attention
                \draw[fill=AIRed!50] (5.2,\i*2-0.8) rectangle (7.8,\i*2-0.6);
                \node at (6.5,\i*2-0.7) {\tiny Masked Attention};
                
                % Cross attention
                \draw[fill=AIGreen] (5.2,\i*2-0.55) rectangle (7.8,\i*2-0.35);
                \node at (6.5,\i*2-0.45) {\tiny Cross Attention};
                
                % Feed forward
                \draw[fill=AIOrange] (5.2,\i*2-0.2) rectangle (7.8,\i*2+0.2);
                \node at (6.5,\i*2) {\tiny Feed Forward};
            }
            
            % Input/Output
            \node[rectangle,draw,fill=AILightBlue] (input) at (1.5,-0.5) {Input Embeddings};
            \node[rectangle,draw,fill=AILightBlue] (output) at (6.5,-0.5) {Output Embeddings};
            
            % Final layer
            \node[rectangle,draw,fill=AIBlue] (linear) at (6.5,6.5) {Linear \& Softmax};
            
            % Arrows
            \draw[->] (input) -- (1.5,0.5);
            \draw[->] (output) -- (6.5,0.5);
            \draw[->] (6.5,5.5) -- (linear);
            
            % Cross connections
            \foreach \i in {1,2,3} {
                \draw[->] (3,\i*2-0.25) -- (5,\i*2-0.25);
            }
            
            % Labels
            \node[below] at (1.5,-1) {\small 编码器};
            \node[below] at (6.5,-1) {\small 解码器};
        \end{tikzpicture}
    \end{center>
    
    \vspace{0.3cm}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{Encoder层结构}
                \begin{itemize}
                    \item Multi-Head Attention：多头自注意力
                    \item Add \& Norm：残差连接和层归一化
                    \item Feed Forward：前馈神经网络
                    \item Add \& Norm：残差连接和层归一化
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column}{0.5\textwidth}
            \begin{block}{Decoder层结构}
                \begin{itemize}
                    \item Masked Multi-Head Attention：掩码自注意力
                    \item Cross Attention：编码器-解码器注意力
                    \item Feed Forward：前馈神经网络
                    \item 每层都有残差连接和层归一化
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
\end{frame>

\section{第3部分：Tokenization}

\begin{frame}{Token：AI理解语言的基本单位}
    \framesubtitle{Tokens: Basic Units for AI Language Understanding}
    
    \begin{columns}
        \begin{column}{0.6\textwidth}
            \begin{block}{Token的定义}
                \begin{itemize>
                    \item \textbf{基本单位}：AI模型处理文本的最小单位
                    \item \textbf{数字表示}：将文本转换为数字序列
                    \item \textbf{统一格式}：不同语言和符号的统一表示
                    \item \textbf{模型输入}：神经网络的实际输入格式
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{为什么需要Tokenization？}
                \begin{itemize>
                    \item 计算机只能处理数字，不能直接处理文字
                    \item 将不同类型的文本统一为数字序列
                    \item 合适的分词能提高处理效率
                    \item 尽可能保持原文的语义信息
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column}{0.4\textwidth}
            \begin{block}{Token的类型}
                \begin{itemize>
                    \item \textbf{字符级}：每个字符是一个Token
                    \item \textbf{词级}：每个单词是一个Token
                    \item \textbf{子词级}：介于字符和单词之间
                    \item \textbf{句子级}：整个句子作为Token
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{实际例子}
                \small
                \textbf{原文}："Hello, world!" \\
                \vspace{0.2cm}
                \textbf{字符级}：["H","e","l","l","o",",","world","!"] \\
                \textbf{词级}：["Hello",",","world","!"] \\
                \textbf{子词级}：["Hello",",","wor","ld","!"]
            \end{exampleblock>
        \end{column>
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{center}
        \begin{tikzpicture}[scale=0.8]
            % Tokenization process
            \node[rectangle,draw,fill=AILightBlue,minimum width=4cm] (text) at (0,0) {"Hello, world!"};
            
            \draw[->] (2,0) -- (4,0);
            \node[above] at (3,0.2) {\tiny Tokenization};
            
            \foreach \i/\token in {1/Hello,2/{,},3/world,4/!} {
                \node[rectangle,draw,fill=AIGreen] (token\i) at (5+\i*1.2,0) {\token};
            }
            
            \draw[->] (9.8,0) -- (11.8,0);
            \node[above] at (10.8,0.2) {\tiny To Numbers};
            
            \foreach \i/\num in {1/1234,2/5,3/2567,4/33} {
                \node[rectangle,draw,fill=AIOrange] (num\i) at (12+\i*1.2,0) {\num};
            }
            
            \node[below] at (0,-0.5) {\tiny 原始文本};
            \node[below] at (7.4,-0.5) {\tiny Token序列};
            \node[below] at (14.4,-0.5) {\tiny 数字序列};
        \end{tikzpicture}
    \end{center>
\end{frame>

\begin{frame}{分词策略：各有优劣的选择}
    \framesubtitle{Tokenization Strategies: Trade-offs and Choices}
    
    \begin{columns}[t]
        \begin{column}{0.32\textwidth}
            \begin{block}{\small 字符级分词}
                \textbf{Character-level}
                
                \vspace{0.2cm}
                
                \textcolor{AIGreen}{\textbf{优点：}}
                \begin{itemize}
                    \item 词汇表小，参数少
                    \item 没有未知词问题
                    \item 适合处理拼写错误
                    \item 支持任意语言
                \end{itemize}
                
                \vspace{0.2cm}
                
                \textcolor{AIRed}{\textbf{缺点：}}
                \begin{itemize}
                    \item 序列长度很长
                    \item 难以捕捉词级语义
                    \item 计算效率低
                    \item 训练困难
                \end{itemize}
            \end{block>
        \end{column>
        
        \begin{column}{0.32\textwidth}
            \begin{block}{\small 词级分词}
                \textbf{Word-level}
                
                \vspace{0.2cm}
                
                \textcolor{AIGreen}{\textbf{优点：}}
                \begin{itemize>
                    \item 保持词的完整语义
                    \item 序列长度适中
                    \item 符合人类理解习惯
                    \item 处理效率高
                \end{itemize>
                
                \vspace{0.2cm}
                
                \textcolor{AIRed}{\textbf{缺点：}}
                \begin{itemize>
                    \item 词汇表巨大
                    \item 存在未知词问题
                    \item 难处理形态变化
                    \item 不同语言差异大
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column}{0.32\textwidth}
            \begin{block}{\small 子词级分词}
                \textbf{Subword-level}
                
                \vspace{0.2cm}
                
                \textcolor{AIGreen}{\textbf{优点：}}
                \begin{itemize>
                    \item 平衡词汇表和语义
                    \item 处理未知词能力强
                    \item 适应多种语言
                    \item 现代模型主流选择
                \end{itemize>
                
                \vspace{0.2cm}
                
                \textcolor{AIRed}{\textbf{缺点：}}
                \begin{itemize>
                    \item 分词结果不够直观
                    \item 需要额外分词算法
                    \item 可能破坏词完整性
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{exampleblock}{主流子词分词算法}
        \begin{itemize}
            \item \textbf{BPE（Byte Pair Encoding）}：基于频率的合并算法
            \item \textbf{WordPiece}：Google开发，BERT使用
            \item \textbf{SentencePiece}：支持多语言的统一分词
            \item \textbf{Unigram}：基于概率的分词方法
        \end{itemize>
    \end{exampleblock>
\end{frame>

\begin{frame}{BPE：现代LLM的主流分词算法}
    \framesubtitle{BPE: Mainstream Tokenization for Modern LLMs}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{BPE算法原理}
                \begin{itemize>
                    \item \textbf{初始状态}：从字符级开始
                    \item \textbf{统计频率}：统计相邻字符对的出现频率
                    \item \textbf{迭代合并}：反复合并最频繁的字符对
                    \item \textbf{构建词汇表}：逐步构建子词词汇表
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{算法步骤}
                \begin{enumerate>
                    \item \textbf{初始化}：将所有文本分解为字符
                    \item \textbf{统计}：计算所有相邻字符对的频率
                    \item \textbf{合并}：合并频率最高的字符对
                    \item \textbf{更新}：更新文本和频率统计
                    \item \textbf{重复}：重复2-4直到达到目标词汇表大小
                \end{enumerate>
            \end{block>
        \end{column>
        
        \begin{column}{0.5\textwidth}
            \begin{exampleblock}{BPE示例}
                \small
                \textbf{初始文本}：["low", "lower", "newest", "widest"] \\
                \textbf{字符级}：["l o w", "l o w e r", "n e w e s t", "w i d e s t"] \\
                
                \vspace{0.2cm}
                
                \textbf{迭代1}：合并最频繁的"e s" → "es" \\
                结果：["l o w", "l o w e r", "n e w es t", "w i d es t"] \\
                
                \vspace{0.2cm}
                
                \textbf{迭代2}：合并最频繁的"es t" → "est" \\
                结果：["l o w", "l o w e r", "n e w est", "w i d est"] \\
                
                \vspace{0.2cm}
                
                继续迭代...
            \end{exampleblock>
            
            \vspace{0.3cm}
            
            \begin{block}{BPE的优势}
                \begin{itemize>
                    \item 数据驱动：基于实际数据的频率统计
                    \item 平衡性：平衡词汇表大小和语义保持
                    \item 适应性：能够适应不同语言和领域
                    \item 效率：算法简单，计算效率高
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
\end{frame>

\section{第4部分：LLM训练过程}

\begin{frame}{LLM训练：从数据到智能的转化}
    \framesubtitle{LLM Training: From Data to Intelligence}
    
    \begin{center}
        \begin{tikzpicture}[scale=0.9]
            % Training pipeline
            \node[rectangle,draw,fill=AILightBlue,minimum width=2cm] (data) at (0,0) {原始数据};
            
            \draw[->] (1.2,0) -- (2.3,0);
            \node[rectangle,draw,fill=AIGreen,minimum width=2cm] (preprocess) at (3.5,0) {数据预处理};
            
            \draw[->] (4.7,0) -- (5.8,0);
            \node[rectangle,draw,fill=AIBlue,minimum width=2cm] (pretrain) at (7,0) {预训练};
            
            \draw[->] (8.2,0) -- (9.3,0);
            \node[rectangle,draw,fill=AIOrange,minimum width=2cm] (finetune) at (10.5,0) {微调};
            
            \draw[->] (11.7,0) -- (12.8,0);
            \node[rectangle,draw,fill=AIRed,minimum width=2cm] (rlhf) at (14,0) {RLHF};
            
            \draw[->] (15.2,0) -- (16.3,0);
            \node[rectangle,draw,fill=AIGray,minimum width=2cm] (deploy) at (17.5,0) {部署应用};
            
            % Time indicators
            \node[below] at (7,-0.8) {\tiny 数周到数月};
            \node[below] at (10.5,-0.8) {\tiny 数小时到数天};
            \node[below] at (14,-0.8) {\tiny 数天到数周};
        \end{tikzpicture}
    \end{center>
    
    \vspace{0.5cm}
    
    \begin{columns}[t]
        \begin{column}{0.32\textwidth}
            \begin{block}{\small 预训练}
                \textbf{Pre-training}
                
                \vspace{0.2cm}
                
                \begin{itemize>
                    \item \textbf{目标}：学习语言基本规律和知识
                    \item \textbf{数据}：大规模无标注文本数据
                    \item \textbf{方法}：自监督学习，预测下一个词
                    \item \textbf{时间}：数周到数月
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column}{0.32\textwidth}
            \begin{block}{\small 微调}
                \textbf{Fine-tuning}
                
                \vspace{0.2cm}
                
                \begin{itemize>
                    \item \textbf{目标}：适应特定任务或领域
                    \item \textbf{数据}：少量高质量标注数据
                    \item \textbf{方法}：有监督学习，任务特定优化
                    \item \textbf{时间}：数小时到数天
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column}{0.32\textwidth}
            \begin{block}{\small RLHF}
                \textbf{Human Feedback RL}
                
                \vspace{0.2cm}
                
                \begin{itemize>
                    \item \textbf{目标}：对齐人类价值观和偏好
                    \item \textbf{数据}：人类标注的偏好数据
                    \item \textbf{方法}：强化学习，奖励模型指导
                    \item \textbf{时间}：数天到数周
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{alertblock}{训练挑战}
        \begin{itemize>
            \item \textbf{规模挑战}：如何高效训练大规模模型 \quad
            \item \textbf{数据质量}：如何确保训练数据的质量 \quad
            \item \textbf{目标对齐}：如何让模型符合人类期望 \quad
            \item \textbf{伦理考虑}：如何避免有害内容的学习
        \end{itemize>
    \end{alertblock>
\end{frame>

\begin{frame}{预训练：奠定语言理解的基础}
    \framesubtitle{Pre-training: Foundation of Language Understanding}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{预训练的核心思想}
                \begin{itemize>
                    \item \textbf{自监督学习}：从无标注数据中学习
                    \item \textbf{语言建模}：预测下一个词的任务
                    \item \textbf{通用知识}：学习广泛的语言和世界知识
                    \item \textbf{基础能力}：为后续任务奠定基础
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{语言建模任务}
                \textbf{输入}：The cat sat on the [MASK] \\
                \textbf{目标}：预测下一个词是 "mat" \\
                \textbf{损失}：交叉熵损失，衡量预测准确性
            \end{exampleblock>
            
            \vspace{0.3cm}
            
            \begin{block}{预训练的效果}
                \begin{itemize}
                    \item 学会语法、语义、语用知识
                    \item 获得广泛的事实性知识
                    \item 发展基本的逻辑推理能力
                    \item 具备一定的创意生成能力
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column}{0.5\textwidth}
            \begin{block}{预训练数据来源}
                \begin{itemize>
                    \item \textbf{网页文本}：Common Crawl等大规模网页数据
                    \item \textbf{书籍语料}：Project Gutenberg等数字图书
                    \item \textbf{新闻文章}：各大新闻网站的文章
                    \item \textbf{百科全书}：Wikipedia等知识性文本
                    \item \textbf{论坛讨论}：Reddit等社交平台内容
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{数据预处理}
                \begin{itemize>
                    \item 清洗过滤：去除低质量和有害内容
                    \item 去重处理：避免重复内容的影响
                    \item 格式统一：统一文本格式和编码
                    \item 质量筛选：基于启发式规则筛选
                    \item 多语言处理：处理不同语言的文本
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{center}
                \begin{tikzpicture}[scale=0.6]
                    % Training process
                    \node[rectangle,draw,fill=AILightBlue] (input) at (0,2) {输入文本};
                    \node[rectangle,draw,fill=AIGreen] (model) at (3,2) {语言模型};
                    \node[rectangle,draw,fill=AIOrange] (pred) at (6,2) {预测词};
                    \node[rectangle,draw,fill=AIRed] (loss) at (3,0) {损失计算};
                    
                    \draw[->] (input) -- (model);
                    \draw[->] (model) -- (pred);
                    \draw[->] (pred) -- (loss);
                    \draw[->] (loss) -- (model);
                    
                    \node[above] at (1.5,2.3) {\tiny 编码};
                    \node[above] at (4.5,2.3) {\tiny 生成};
                    \node[right] at (6.5,1) {\tiny 反向传播};
                \end{tikzpicture}
            \end{center>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{人类反馈强化学习（RLHF）}
    \framesubtitle{RLHF: Aligning AI with Human Values}
    
    \begin{block}{RLHF的动机}
        \begin{itemize>
            \item \textbf{价值对齐}：让AI的行为符合人类价值观
            \item \textbf{质量提升}：提高生成内容的质量
            \item \textbf{安全考虑}：减少有害或不当内容的生成
            \item \textbf{用户体验}：提供更符合用户期望的回答
        \end{itemize>
    \end{block>
    
    \vspace{0.3cm}
    
    \begin{center}
        \begin{tikzpicture}[scale=0.8]
            % RLHF process
            \node[rectangle,draw,fill=AILightBlue,minimum width=3cm] (step1) at (0,3) {1. 收集人类偏好数据};
            \node[rectangle,draw,fill=AIGreen,minimum width=3cm] (step2) at (6,3) {2. 训练奖励模型};
            \node[rectangle,draw,fill=AIOrange,minimum width=3cm] (step3) at (3,0) {3. 强化学习优化};
            
            \draw[->] (step1) -- (step2);
            \draw[->] (step2) -- (step3);
            \draw[->] (step3) -- (step1);
            
            % Details for each step
            \node[below,text width=2.5cm,align=center] at (0,2) {\tiny 训练有素的人类标注员对模型输出进行两两比较};
            \node[below,text width=2.5cm,align=center] at (6,2) {\tiny 学习人类的偏好模式，输出质量评分};
            \node[below,text width=2.5cm,align=center] at (3,-1) {\tiny 使用PPO等算法优化，最大化奖励模型评分};
        \end{tikzpicture}
    \end{center>
    
    \vspace{0.3cm}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{RLHF的效果}
                \begin{itemize>
                    \item \success{有用性}：回答更有帮助和相关
                    \item \success{无害性}：减少有害内容的生成
                    \item \success{诚实性}：减少虚假信息的产生
                    \item \success{对话质量}：提高对话自然度和连贯性
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column}{0.5\textwidth}
            \begin{alertblock}{挑战与限制}
                \begin{itemize>
                    \item 成本高昂：需要大量人工标注
                    \item 主观性：人类偏好存在主观性
                    \item 文化差异：不同文化背景的偏好差异
                    \item 规模限制：难以覆盖所有可能场景
                \end{itemize>
            \end{alertblock>
        \end{column>
    \end{columns>
\end{frame>

\section{第5部分：能力边界分析}

\begin{frame}{LLM的超能力：理解、生成、推理}
    \framesubtitle{LLM Superpowers: Understanding, Generation, Reasoning}
    
    \begin{columns}[t]
        \begin{column}{0.48\textwidth}
            \begin{block}{强大的语言理解能力}
                \begin{itemize>
                    \item \success{深度理解}：理解文本深层含义和隐含信息
                    \item \success{上下文感知}：基于长上下文进行准确理解
                    \item \success{语义推理}：进行复杂的语义推理和关联
                    \item \success{意图识别}：准确识别用户的真实意图
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{卓越的内容生成能力}
                \begin{itemize>
                    \item \success{多样化写作}：适应不同风格、体裁、受众
                    \item \success{创意生成}：产生新颖的想法和创意内容
                    \item \success{格式适配}：生成各种格式和结构的内容
                    \item \success{结构化输出}：按要求组织信息结构
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{出色的推理能力}
                \begin{itemize>
                    \item \success{逻辑推理}：进行复杂的逻辑分析和推理
                    \item \success{因果关系}：理解和分析因果关系
                    \item \success{数据分析}：从数据中提取洞察和结论
                    \item \success{问题解决}：分解和解决复杂问题
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column}{0.48\textwidth}
            \begin{block}{广泛的知识覆盖}
                \begin{itemize>
                    \item 百科知识：涵盖各个领域的基础知识
                    \item 专业知识：在多个专业领域有深入了解
                    \item 多语言能力：支持多种语言的理解和生成
                    \item 时事了解：掌握训练数据截止时间前的信息
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{灵活的学习适应能力}
                \begin{itemize>
                    \item 快速学习：通过少量示例快速适应新任务
                    \item 上下文学习：在对话中学习和适应
                    \item 个性化：根据用户偏好调整输出风格
                    \item 跨领域迁移：在不同领域间迁移知识
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{在传媒中的优势体现}
                \begin{itemize>
                    \item 高质量的新闻、文章、脚本创作
                    \item 深度的文本分析和信息提取
                    \item 自然流畅的用户对话和服务
                    \item 基于用户需求的定制化内容
                \end{itemize>
            \end{exampleblock>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{理性认识：LLM的局限性与挑战}
    \framesubtitle{Rational Understanding: LLM Limitations \& Challenges}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{alertblock}{"幻觉"现象详解}
                \textbf{定义}：模型生成看似合理但实际错误的信息
                
                \vspace{0.2cm}
                
                \textbf{表现形式}：
                \begin{itemize>
                    \item 编造不存在的事实
                    \item 虚构人名、地名、数据
                    \item 创造虚假的引用和来源
                    \item 生成错误的数学计算
                \end{itemize>
                
                \vspace{0.2cm}
                
                \textbf{产生原因}：
                \begin{itemize>
                    \item 基于模式匹配而非真实理解
                    \item 训练数据中的错误信息
                    \item 生成压力下的"创造"倾向
                    \item 基于概率的生成机制
                \end{itemize>
            \end{alertblock>
        \end{column>
        
        \begin{column}{0.5\textwidth}
            \begin{block}{其他主要局限性}
                \begin{itemize>
                    \item \warning{知识截止}：无法获取训练后的最新信息
                    \item \warning{数学计算}：在复杂数学运算上的不足
                    \item \warning{事实核查}：无法实时验证信息准确性
                    \item \warning{记忆限制}：上下文长度的物理限制
                    \item \warning{一致性问题}：同样问题可能得到不同答案
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{在传媒应用中的风险}
                \begin{itemize>
                    \item 可能生成虚假新闻信息
                    \item 统计数据和事实的错误
                    \item 关于真实人物的错误信息
                    \item 对历史事件的错误描述
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{exampleblock}{应对策略}
        \begin{itemize>
            \item \textbf{人工审核}：建立严格的人工审核机制 \quad
            \item \textbf{多源验证}：交叉验证重要信息 \quad
            \item \textbf{知识库辅助}：结合可靠的知识库 \quad
            \item \textbf{置信度评估}：评估生成内容的可信度 \quad
            \item \textbf{风险提示}：向用户明确AI的局限性
        \end{itemize>
    \end{exampleblock>
\end{frame>

\begin{frame}{善用AI：发挥优势，规避风险}
    \framesubtitle{Using AI Wisely: Leverage Strengths, Mitigate Risks}
    
    \begin{columns}
        \begin{column}{0.48\textwidth}
            \begin{block}{使用原则}
                \begin{itemize>
                    \item \textbf{明确定位}：将LLM作为辅助工具而非替代品
                    \item \textbf{验证为先}：对重要信息进行独立验证
                    \item \textbf{迭代改进}：通过反馈不断优化使用效果
                    \item \textbf{伦理考虑}：确保使用符合伦理和法律要求
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{适合的应用场景}
                \begin{itemize>
                    \item \success{创意启发}：头脑风暴和创意生成
                    \item \success{内容草稿}：初稿写作和内容框架
                    \item \success{信息整理}：文本摘要和信息提取
                    \item \success{格式转换}：内容的格式化和重组
                    \item \success{对话交互}：用户咨询和客服支持
                \end{itemize>
            \end{exampleblock>
        \end{column>
        
        \begin{column}{0.48\textwidth}
            \begin{alertblock}{需要谨慎的场景}
                \begin{itemize>
                    \item \warning{事实性信息}：具体的数据、日期、人名等
                    \item \warning{法律建议}：法律相关的专业建议
                    \item \warning{医疗信息}：健康和医疗相关信息
                    \item \warning{金融建议}：投资和财务相关建议
                    \item \warning{科学计算}：精确的数学和科学计算
                \end{itemize>
            \end{alertblock>
            
            \vspace{0.3cm}
            
            \begin{block}{质量控制策略}
                \begin{itemize>
                    \item 人机协作：结合人类专业知识和AI能力
                    \item 多重检查：建立多层次的质量检查机制
                    \item 基准测试：定期测试AI的性能表现
                    \item 文档记录：记录AI使用的过程和结果
                    \item 持续监控：持续监控AI输出的质量
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{center}
        \begin{tikzpicture}[scale=0.7]
            % Decision tree for AI usage
            \node[rectangle,draw,fill=AIBlue] (start) at (0,0) {AI任务};
            
            \node[rectangle,draw,fill=AIGreen] (creative) at (-3,-2) {创意任务};
            \node[rectangle,draw,fill=AIOrange] (factual) at (0,-2) {事实任务};
            \node[rectangle,draw,fill=AIRed] (critical) at (3,-2) {关键任务};
            
            \draw[->] (start) -- (creative);
            \draw[->] (start) -- (factual);
            \draw[->] (start) -- (critical);
            
            \node[below] at (-3,-2.8) {\tiny 可直接使用};
            \node[below] at (0,-2.8) {\tiny 需要验证};
            \node[below] at (3,-2.8) {\tiny 谨慎使用};
            
            \node[left] at (-1.5,-1) {\tiny 创意};
            \node[above] at (0,-1) {\tiny 事实};
            \node[right] at (1.5,-1) {\tiny 关键};
        \end{tikzpicture}
    \end{center>
\end{frame>

\begin{frame}{第2周总结：深入理解LLM的技术基础}
    \framesubtitle{Week 2 Summary: Deep Understanding of LLM Technical Foundations}
    
    \begin{columns}[t]
        \begin{column>{0.48\textwidth}
            \begin{block}{本周重点回顾}
                \textbf{Transformer架构}：
                \begin{itemize>
                    \item 注意力机制的工作原理
                    \item 多头注意力的优势
                    \item 位置编码的重要性
                    \item 架构的革命性影响
                \end{itemize>
                
                \vspace{0.2cm}
                
                \textbf{Tokenization技术}：
                \begin{itemize>
                    \item Token的概念和重要性
                    \item 不同分词方法的对比
                    \item BPE算法的原理
                    \item 分词对模型理解的影响
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column>{0.48\textwidth}
            \begin{block}{关键概念掌握}
                \textbf{LLM训练过程}：
                \begin{itemize>
                    \item 预训练的核心思想
                    \item 微调技术的应用
                    \item 指令微调的价值
                    \item RLHF的重要作用
                \end{itemize>
                
                \vspace{0.2cm}
                
                \textbf{能力边界认知}：
                \begin{itemize>
                    \item LLM的核心优势
                    \item 局限性和"幻觉"现象
                    \item 正确使用的原则
                    \item 质量控制策略
                \end{itemize}
            \end{block>
        \end{column>
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{exampleblock}{实际应用启示}
        \begin{itemize>
            \item \textbf{工具定位}：将LLM作为强大的辅助工具 \quad
            \item \textbf{验证机制}：建立严格的内容验证流程 \quad
            \item \textbf{场景选择}：在合适的场景中发挥LLM优势 \quad
            \item \textbf{风险控制}：充分认识和控制使用风险
        \end{itemize>
    \end{exampleblock>
    
    \vspace{0.3cm}
    
    \begin{center}
        \begin{block}{下周预告：第3周 - 提示词工程基础}
            \textbf{学习目标}：掌握与AI有效沟通的艺术 \\
            \textbf{主要内容}：提示词的核心地位、CRISPE框架、四种基础提示模式 \\
            \textbf{实践重点}：设计有效的提示词，避免常见错误，提示词诊所实践
        \end{block>
    \end{center>
\end{frame>

\begin{frame}[plain]
    \begin{center}
        \begin{tikzpicture}[remember picture,overlay]
            \fill[AIBlue] (current page.south west) rectangle (current page.north east);
            \node[white,font=\Huge\bfseries] at (current page.center) {谢谢！};
            \node[white,font=\Large] at ([yshift=-1cm]current page.center) {Thank You!};
            \node[white,font=\large] at ([yshift=-2cm]current page.center) {下周见 See You Next Week};
        \end{tikzpicture>
    \end{center>
\end{frame>

\end{document}