% Week 3: Prompt Engineering Fundamentals
\input{ai_course_template}

\title{第3周：提示词工程基础}
\subtitle{Prompt Engineering Fundamentals}
\author{课程教师}
\institute{长江新闻与传播学院 \\ 汕头大学}
\date{第3周课程}

\begin{document}

% Title slide
\begin{frame}[plain]
    \titlepage
\end{frame}

% Table of contents
\begin{frame}{课程概览}
    \tableofcontents
\end{frame}

\section{第1部分：提示词的重要性}

\begin{frame}{提示词：连接人类智慧与AI能力的桥梁}
    \framesubtitle{Prompts: Bridging Human Intelligence and AI Capabilities}
    
    \begin{columns}
        \begin{column}{0.6\textwidth}
            \begin{block}{什么是提示词（Prompt）？}
                \begin{itemize>
                    \item \textbf{定义}：用户向AI模型输入的指令、问题或描述
                    \item \textbf{作用}：人类意图与AI理解之间的桥梁
                    \item \textbf{目标}：引导AI生成期望的输出结果
                    \item \textbf{工具}：控制AI行为的主要手段
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{提示词的重要性}
                \begin{itemize>
                    \item \success{决定输出质量}：好的提示词带来好的结果
                    \item \success{影响AI理解}：直接影响AI对任务的理解
                    \item \success{提升效率}：减少反复调试的时间
                    \item \success{节约成本}：减少API调用次数和计算资源
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column>{0.4\textwidth}
            \begin{center>
                \begin{tikzpicture}[scale=0.7]
                    % Bridge metaphor
                    \node[rectangle,draw,fill=AILightBlue,minimum width=2cm] (human) at (0,2) {人类意图};
                    \node[rectangle,draw,fill=AIGreen,minimum width=2cm] (ai) at (6,2) {AI理解};
                    
                    % Bridge
                    \draw[thick,AIBlue] (1.2,1.8) -- (4.8,1.8);
                    \draw[thick,AIBlue] (1.2,2.2) -- (4.8,2.2);
                    \node[above] at (3,2.5) {\textbf{提示词}};
                    
                    % Support pillars
                    \foreach \x in {2,3,4,5} {
                        \draw[thick,AIGray] (\x,1.8) -- (\x,1.2);
                    }
                    
                    % Labels
                    \node[below] at (0,1.5) {\tiny 需求想法};
                    \node[below] at (6,1.5) {\tiny 准确输出};
                    \node[below] at (3,0.8) {\tiny 沟通桥梁};
                \end{tikzpicture}
            \end{center>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{生活中的类比}
                \begin{itemize>
                    \item 餐厅点菜：清晰描述得到满意菜肴
                    \item 问路指引：详细描述获得准确方向
                    \item 图书馆咨询：明确需求得到精准推荐
                    \item 艺术委托：具体要求创作理想作品
                \end{itemize>
            \end{exampleblock>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{从简单指令到复杂工程：提示词的演进}
    \framesubtitle{Evolution of Prompts: From Simple Commands to Complex Engineering}
    
    \begin{center}
        \begin{tikzpicture}[scale=0.9]
            % Timeline
            \draw[thick,AIBlue,->] (0,0) -- (12,0);
            
            % Three phases
            \foreach \x/\year/\phase in {2/2018-2019/简单指令期,6/2020-2021/模式探索期,10/2022年至今/工程化时代} {
                \draw[thick,AIBlue] (\x,-0.3) -- (\x,0.3);
                \node[above,font=\small\bfseries] at (\x,0.8) {\phase};
                \node[below,font=\tiny] at (\x,-0.8) {\year};
            }
        \end{tikzpicture}
    \end{center>
    
    \vspace{0.5cm}
    
    \begin{columns}[t]
        \begin{column}{0.32\textwidth}
            \begin{block}{\small 第一阶段：简单指令期}
                \textbf{2018-2019年}
                
                \vspace{0.2cm}
                
                \textbf{特点}：简单的关键词和短句 \\
                \textbf{效果}：基础的分类和生成任务 \\
                \textbf{局限}：功能有限，效果不稳定
                
                \vspace{0.2cm}
                
                \begin{exampleblock}{\tiny 例子}
                    "翻译：Hello World" \\
                    "分类：这是一篇体育新闻"
                \end{exampleblock>
            \end{block>
        \end{column>
        
        \begin{column}{0.32\textwidth}
            \begin{block}{\small 第二阶段：模式探索期}
                \textbf{2020-2021年}
                
                \vspace{0.2cm}
                
                \textbf{特点}：开始探索复杂的提示模式 \\
                \textbf{突破}：Few-shot学习的发现 \\
                \textbf{创新}：Chain-of-Thought等技术出现
                
                \vspace{0.2cm}
                
                \begin{exampleblock}{\tiny 例子}
                    以下是一些例子： \\
                    输入：苹果 → 输出：水果 \\
                    输入：汽车 → 输出：交通工具 \\
                    输入：钢琴 → 输出：
                \end{exampleblock>
            \end{block>
        \end{column>
        
        \begin{column}{0.32\textwidth}
            \begin{block}{\small 第三阶段：工程化时代}
                \textbf{2022年至今}
                
                \vspace{0.2cm}
                
                \textbf{特点}：系统化的提示词设计方法 \\
                \textbf{理论}：形成了完整的理论体系 \\
                \textbf{工具}：专门的提示词工程工具
                
                \vspace{0.2cm}
                
                \begin{exampleblock}{\tiny 例子}
                    你是一位资深的新闻编辑...
                    请根据以下信息写一篇新闻报道：
                    要求：客观、准确、吸引人...
                \end{exampleblock>
            \end{block>
        \end{column>
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{exampleblock}{对传媒行业的影响}
        \begin{itemize>
            \item 革命性地改变内容创作方式 \quad 
            \item 大幅提高工作效率 \quad
            \item 创造新的内容形式和服务 \quad
            \item 成为传媒人必备的新技能
        \end{itemize>
    \end{exampleblock>
\end{frame>

\section{第2部分：CRISPE框架详解}

\begin{frame}{CRISPE框架：构建优质提示词的系统方法}
    \framesubtitle{CRISPE Framework: Systematic Approach to Quality Prompts}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{CRISPE框架简介}
                \begin{itemize>
                    \item \textbf{目标}：提供系统化的提示词设计方法
                    \item \textbf{结构}：五个核心要素的有机组合
                    \item \textbf{效果}：显著提升提示词的质量和效果
                    \item \textbf{应用}：适用于各种类型的AI任务
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{框架优势}
                \begin{itemize>
                    \item 系统性：全面覆盖提示词设计的各个方面
                    \item 针对性：每个要素都有明确的作用
                    \item 可操作性：提供具体的操作指导
                    \item 可重复性：确保结果的一致性
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column>{0.5\textwidth}
            \begin{center>
                \begin{tikzpicture}[scale=0.8]
                    % CRISPE circle
                    \foreach \i/\letter/\color/\desc in {
                        0/C/AIBlue/Capacity,
                        1/R/AIGreen/Insight,
                        2/I/AIOrange/Statement,
                        3/S/AIRed/Personality,
                        4/P/AIGray/Experiment
                    } {
                        \pgfmathsetmacro{\angle}{72*\i}
                        \node[circle,draw,fill=\color,minimum size=1cm,font=\Large\bfseries] 
                             (node\i) at (\angle:2) {\letter};
                        \node[below,font=\tiny] at (\angle:2.7) {\desc};
                    }
                    
                    % Center
                    \node[circle,draw,fill=AILightBlue,minimum size=1.5cm,font=\bfseries] 
                         at (0,0) {CRISPE};
                    
                    % Connections
                    \foreach \i in {0,1,2,3,4} {
                        \pgfmathsetmacro{\j}{mod(\i+1,5)}
                        \draw[->] (node\i) -- (node\j);
                    }
                \end{tikzpicture}
            \end{center>
            
            \vspace{0.3cm}
            
            \begin{block}{CRISPE五要素}
                \begin{itemize>
                    \item \textbf{C} - Capacity and Role (能力与角色)
                    \item \textbf{R} - Insight (洞察背景)
                    \item \textbf{I} - Statement (任务陈述)
                    \item \textbf{S} - Personality (个性风格)
                    \item \textbf{P} - Experiment (实验迭代)
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{C - 角色设定：让AI扮演专业角色}
    \framesubtitle{C - Capacity and Role: Professional Role Assignment}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{角色设定的重要性}
                \begin{itemize>
                    \item \textbf{身份认同}：让AI明确自己的身份和职责
                    \item \textbf{知识激活}：激活相关领域的专业知识
                    \item \textbf{行为引导}：引导AI采用专业的思维方式
                    \item \textbf{输出质量}：提升输出内容的专业性
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block>{角色设定的要素}
                \begin{itemize>
                    \item \textbf{专业身份}：明确的职业或专业角色
                    \item \textbf{经验背景}：相关的工作经验和资历
                    \item \textbf{专业能力}：具备的专业技能和知识
                    \item \textbf{权威性}：在该领域的地位和声誉
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column>{0.5\textwidth}
            \begin{exampleblock}{传媒领域的角色示例}
                \textbf{资深记者}：
                \small
                "你是一位有15年经验的调查记者，擅长深度报道和事实核查，曾获得普利策新闻奖，在政治和社会议题报道方面有丰富经验。"
                
                \vspace{0.2cm}
                
                \textbf{电视制片人}：
                \small
                "你是一位知名电视制片人，制作过多部获奖纪录片，擅长故事叙述和视觉呈现，对观众心理有深入理解。"
                
                \vspace{0.2cm}
                
                \textbf{社交媒体专家}：
                \small
                "你是一位社交媒体营销专家，管理过多个百万粉丝账号，精通各平台算法和用户行为。"
            \end{exampleblock>
            
            \vspace{0.3cm>
            
            \begin{alertblock>{常见错误}
                \begin{itemize>
                    \item 过于宽泛："你是一个专家"
                    \item 缺乏背景：只说角色不说经验
                    \item 不够权威：缺乏可信度的建立
                    \item 角色冲突：设定相互矛盾的角色
                \end{itemize>
            \end{alertblock>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{R - 洞察背景：为AI提供充分的上下文}
    \framesubtitle{R - Insight: Providing Comprehensive Context}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{block}{背景信息的价值}
                \begin{itemize>
                    \item \textbf{理解深化}：帮助AI更深入理解任务
                    \item \textbf{精准定位}：明确任务的具体要求和目标
                    \item \textbf{情境感知}：了解任务所处的环境和条件
                    \item \textbf{质量提升}：显著提升输出内容的相关性
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{背景信息的类型}
                \begin{itemize>
                    \item \textbf{任务背景}：任务的起因、目的、重要性
                    \item \textbf{受众信息}：目标受众的特征和需求
                    \item \textbf{环境条件}：时间、地点、文化等环境因素
                    \item \textbf{预期效果}：希望达到的目标和效果
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{提供背景的技巧}
                \begin{itemize>
                    \item 简洁明了：避免冗长的背景描述
                    \item 重点突出：强调最重要的背景信息
                    \item 结构清晰：用条目或段落清晰组织
                    \item 相关性强：确保背景信息与任务直接相关
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column>{0.5\textwidth}
            \begin{exampleblock}{传媒场景的背景示例}
                \textbf{新闻报道背景}：
                \small
                "背景：这是一篇关于人工智能在教育领域应用的深度报道。目标受众是关注科技发展的普通读者，需要平衡专业性和可读性。发布平台是主流新闻网站，预期阅读时间5-8分钟。"
                
                \vspace{0.2cm}
                
                \textbf{社交媒体内容背景}：
                \small
                "背景：为一家科技公司的新产品发布会制作微博内容。目标是吸引年轻用户关注，增加转发和讨论。发布时间是工作日晚上8点，需要考虑用户的休闲状态。"
                
                \vspace{0.2cm>
                
                \textbf{视频脚本背景}：
                \small
                "背景：制作一个5分钟的企业宣传视频脚本。目标受众是潜在投资者和合作伙伴。需要突出公司的创新能力和市场前景。"
            \end{exampleblock>
            
            \vspace{0.3cm}
            
            \begin{alertblock}{常见问题}
                \begin{itemize>
                    \item 信息过载：提供过多无关信息
                    \item 信息不足：缺乏必要的上下文
                    \item 信息模糊：背景描述不够具体
                    \item 信息过时：使用过时或不准确的信息
                \end{itemize>
            \end{alertblock>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{I - 任务陈述：明确具体的执行指令}
    \framesubtitle{I - Statement: Clear and Specific Task Instructions}
    
    \begin{columns}
        \begin{column>{0.5\textwidth}
            \begin{block}{任务陈述的核心要素}
                \begin{itemize>
                    \item \textbf{动作动词}：明确要执行的具体动作
                    \item \textbf{输出要求}：详细说明期望的输出格式
                    \item \textbf{质量标准}：明确的质量和评判标准
                    \item \textbf{约束条件}：时间、篇幅、风格等限制
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{有效动作动词的选择}
                \begin{itemize>
                    \item \textbf{创作类}：写作、创建、设计、构思
                    \item \textbf{分析类}：分析、评估、比较、总结
                    \item \textbf{转换类}：翻译、改写、转换、适配
                    \item \textbf{整理类}：整理、分类、提取、归纳
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{exampleblock>{任务陈述的结构模板}
                \small
                请[动作动词][具体对象]，要求： \\
                1. [输出格式要求] \\
                2. [内容质量标准] \\
                3. [风格和语调] \\
                4. [篇幅限制] \\
                5. [其他特殊要求]
            \end{exampleblock>
        \end{column>
        
        \begin{column>{0.5\textwidth}
            \begin{exampleblock}{传媒任务陈述示例}
                \textbf{新闻写作任务}：
                \small
                "请写一篇关于人工智能发展的新闻报道，要求：
                1. 采用倒金字塔结构，包含标题、导语、正文
                2. 语言客观中性，避免主观评价
                3. 字数控制在800-1000字
                4. 包含至少3个具体数据或案例
                5. 适合普通读者阅读理解"
                
                \vspace{0.2cm}
                
                \textbf{数据分析任务}：
                \small
                "请分析以下用户评论数据，要求：
                1. 提取主要观点和情感倾向
                2. 按照正面、负面、中性分类统计
                3. 识别最频繁提及的关键词
                4. 提供改进建议
                5. 以表格和文字结合的形式呈现"
            \end{exampleblock}
            
            \vspace{0.3cm}
            
            \begin{alertblock>{常见错误避免}
                \begin{itemize>
                    \item 任务模糊："帮我写点东西"
                    \item 要求矛盾：同时要求简洁和详细
                    \item 标准不清：没有明确的质量标准
                    \item 过于复杂：一次性要求太多不同任务
                \end{itemize>
            \end{alertblock>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{S - 个性风格：塑造AI的表达特色}
    \framesubtitle{S - Personality: Shaping AI's Expression Style}
    
    \begin{columns}
        \begin{column>{0.5\textwidth}
            \begin{block}{个性风格的重要性}
                \begin{itemize>
                    \item \textbf{品牌一致性}：保持品牌或个人的风格一致
                    \item \textbf{受众适配}：匹配目标受众的偏好和期望
                    \item \textbf{情感连接}：建立与用户的情感联系
                    \item \textbf{差异化}：在众多内容中脱颖而出
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{风格维度的分类}
                \textbf{语言风格}：
                \begin{itemize>
                    \item 正式 ↔ 非正式
                    \item 严肃 ↔ 轻松
                    \item 简洁 ↔ 详细
                    \item 客观 ↔ 主观
                \end{itemize>
                
                \vspace{0.2cm}
                
                \textbf{情感色彩}：
                \begin{itemize>
                    \item 热情 ↔ 冷静
                    \item 乐观 ↔ 谨慎
                    \item 友好 ↔ 专业
                    \item 幽默 ↔ 严肃
                \end{itemize>
            \end{block>
        \end{column}
        
        \begin{column>{0.5\textwidth}
            \begin{exampleblock}{传媒风格设定示例}
                \textbf{新闻报道风格}：
                \small
                "采用客观、准确、简洁的新闻写作风格。语言正式但易懂，避免专业术语，保持中性立场，不带个人情感色彩。"
                
                \vspace{0.2cm}
                
                \textbf{社交媒体风格}：
                \small
                "使用轻松、亲切、有趣的语调，适当使用网络流行语和表情符号，语言简洁有力，富有感染力和互动性。"
                
                \vspace{0.2cm}
                
                \textbf{纪录片解说风格}：
                \small
                "采用深沉、权威、富有感染力的叙述风格，语言优美而有力，善用比喻和排比，能够引发观众的思考和情感共鸣。"
            \end{exampleblock}
            
            \vspace{0.3cm}
            
            \begin{block}{个性化的平衡}
                \begin{itemize>
                    \item 品牌 vs 个性：在品牌要求和个性表达间平衡
                    \item 一致 vs 灵活：保持一致性同时适应不同场景
                    \item 专业 vs 亲和：在专业性和亲和力间找到平衡
                    \item 通用 vs 定制：在通用性和个性化间权衡
                \end{itemize>
            \end{block}
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{P - 实验迭代：持续优化提示词效果}
    \framesubtitle{P - Experiment: Continuous Optimization of Prompt Performance}
    
    \begin{columns}
        \begin{column>{0.5\textwidth}
            \begin{block}{实验迭代的重要性}
                \begin{itemize>
                    \item \textbf{持续改进}：通过实验不断提升效果
                    \item \textbf{科学方法}：基于数据和证据进行优化
                    \item \textbf{精准调优}：找到最适合的参数和设置
                    \item \textbf{效果验证}：验证改进措施的实际效果
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{实验设计的原则}
                \begin{itemize>
                    \item 单变量控制：每次只改变一个变量
                    \item 对照组设置：设置基准对照组
                    \item 量化指标：使用可量化的评估指标
                    \item 重复验证：多次实验确保结果可靠
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{实验的类型}
                \begin{itemize>
                    \item 词汇实验：测试不同词汇的效果
                    \item 结构实验：测试不同的提示词结构
                    \item 角色实验：测试不同的角色设定
                    \item 参数实验：测试不同的参数设置
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column>{0.5\textwidth}
            \begin{center>
                \begin{tikzpicture}[scale=0.7]
                    % Experiment flow
                    \node[rectangle,draw,fill=AIBlue] (target) at (0,3) {确定目标};
                    \node[rectangle,draw,fill=AIGreen] (design) at (3,3) {设计实验};
                    \node[rectangle,draw,fill=AIOrange] (test) at (6,3) {执行测试};
                    \node[rectangle,draw,fill=AIRed] (collect) at (6,1.5) {收集数据};
                    \node[rectangle,draw,fill=AIGray] (analyze) at (3,1.5) {分析结果};
                    \node[rectangle,draw,fill=AILightBlue] (optimize) at (0,1.5) {优化调整};
                    \node[rectangle,draw,fill=AIGreen] (retest) at (1.5,0) {再次测试};
                    \node[rectangle,draw,fill=AIBlue] (confirm) at (4.5,0) {确认效果};
                    
                    % Arrows
                    \draw[->] (target) -- (design);
                    \draw[->] (design) -- (test);
                    \draw[->] (test) -- (collect);
                    \draw[->] (collect) -- (analyze);
                    \draw[->] (analyze) -- (optimize);
                    \draw[->] (optimize) -- (retest);
                    \draw[->] (retest) -- (confirm);
                    \draw[->] (optimize) to[bend left=30] (target);
                \end{tikzpicture}
            \end{center>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{传媒实验案例}
                \textbf{新闻标题优化实验}：
                \small
                \begin{itemize>
                    \item 实验目标：提高新闻标题的点击率
                    \item 变量：标题的长度、情感色彩、关键词位置
                    \item 指标：点击率、分享率、停留时间
                    \item 方法：A/B测试不同版本的标题
                \end{itemize>
            \end{exampleblock>
            
            \vspace{0.3cm}
            
            \begin{block}{迭代优化策略}
                \begin{itemize>
                    \item 渐进式改进：小步快跑，持续优化
                    \item 重点突破：集中精力解决关键问题
                    \item 数据驱动：基于数据而非直觉决策
                    \item 全局考虑：考虑优化对整体效果的影响
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
\end{frame>

\section{第3部分：基础提示模式}

\begin{frame}{基础模式：提示词的四种经典类型}
    \framesubtitle{Basic Patterns: Four Classic Types of Prompts}
    
    \begin{columns}
        \begin{column>{0.5\textwidth}
            \begin{block}{提示模式的重要性}
                \begin{itemize>
                    \item \textbf{结构化思维}：提供清晰的思维框架
                    \item \textbf{效率提升}：快速选择合适的交互方式
                    \item \textbf{效果保证}：经过验证的有效模式
                    \item \textbf{可复用性}：可以在不同场景中重复使用
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{四种基础模式}
                \begin{enumerate>
                    \item \textbf{指令型提示}：直接明确的任务指令
                    \item \textbf{问答型提示}：自然直观的信息获取
                    \item \textbf{补全型提示}：激发创意的开放式引导
                    \item \textbf{对话型提示}：深入交互的智能对话
                \end{enumerate}
            \end{block>
        \end{column}
        
        \begin{column>{0.5\textwidth}
            \begin{center>
                \begin{tabular}{|c|c|c|c|}
                    \hline
                    \tableheadcolor \textbf{模式类型} & \textbf{适用场景} & \textbf{优势} & \textbf{局限性} \\
                    \hline
                    指令型 & 明确任务 & 直接高效 & 缺乏灵活性 \\
                    \hline
                    问答型 & 信息获取 & 自然直观 & 可能不够精确 \\
                    \hline
                    补全型 & 创意生成 & 激发创意 & 结果不可控 \\
                    \hline
                    对话型 & 复杂交互 & 灵活深入 & 效率相对较低 \\
                    \hline
                \end{tabular}
            \end{center>
            
            \vspace{0.3cm}
            
            \begin{block}{在传媒中的应用分布}
                \begin{itemize>
                    \item \textbf{新闻写作}：主要使用指令型和补全型
                    \item \textbf{信息搜集}：主要使用问答型和对话型
                    \item \textbf{创意策划}：主要使用补全型和对话型
                    \item \textbf{数据分析}：主要使用指令型和问答型
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{模式组合使用}
                \begin{itemize>
                    \item 序列组合：按顺序使用不同模式
                    \item 嵌套组合：在一个模式中嵌入其他模式
                    \item 并行组合：同时使用多种模式对比效果
                    \item 动态切换：根据情况动态切换模式
                \end{itemize>
            \end{exampleblock>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{指令型提示：直接明确的任务指令}
    \framesubtitle{Instruction Prompts: Direct and Clear Task Commands}
    
    \begin{columns}
        \begin{column>{0.5\textwidth}
            \begin{block}{指令型提示的特点}
                \begin{itemize>
                    \item \textbf{直接明确}：直接告诉AI要做什么
                    \item \textbf{任务导向}：以完成特定任务为目标
                    \item \textbf{高效执行}：快速获得预期结果
                    \item \textbf{易于控制}：便于控制输出的格式和内容
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{exampleblock}{基本结构}
                \textbf{[动作动词] + [对象] + [要求/条件]}
            \end{exampleblock>
            
            \vspace{0.3cm}
            
            \begin{block}{常用动作动词}
                \begin{itemize>
                    \item \textbf{创作类}：写作、创建、设计、编写
                    \item \textbf{分析类}：分析、评估、比较、总结
                    \item \textbf{转换类}：翻译、改写、转换、优化
                    \item \textbf{整理类}：整理、分类、提取、归纳
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column>{0.5\textwidth}
            \begin{exampleblock>{传媒场景的指令型提示示例}
                \textbf{新闻写作指令}：
                \small
                "写一篇关于人工智能教育应用的新闻报道，要求：
                - 字数800-1000字
                - 包含专家观点和具体案例
                - 采用客观中性的语调
                - 结构清晰，逻辑严密"
                
                \vspace{0.2cm}
                
                \textbf{社交媒体指令}：
                \small
                "为我们的环保产品创作5条微博文案，要求：
                - 每条不超过140字
                - 语调轻松有趣
                - 包含相关话题标签
                - 能够引发用户互动"
                
                \vspace{0.2cm}
                
                \textbf{数据分析指令}：
                \small
                "分析以下用户评论数据，提取：
                - 主要观点和关键词
                - 情感倾向分布
                - 改进建议
                - 以表格形式呈现结果"
            \end{exampleblock>
        \end{column>
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{center>
        \begin{tikzpicture}[scale=0.7]
            % Instruction prompt flow
            \node[rectangle,draw,fill=AIBlue] (action) at (0,0) {动作动词};
            \node[rectangle,draw,fill=AIGreen] (object) at (3,0) {目标对象};
            \node[rectangle,draw,fill=AIOrange] (requirements) at (6,0) {具体要求};
            \node[rectangle,draw,fill=AIRed] (output) at (9,0) {预期输出};
            
            \draw[->] (action) -- (object);
            \draw[->] (object) -- (requirements);
            \draw[->] (requirements) -- (output);
            
            \node[below] at (0,-0.8) {\tiny 写作/分析};
            \node[below] at (3,-0.8) {\tiny 新闻报道};
            \node[below] at (6,-0.8) {\tiny 格式/质量};
            \node[below] at (9,-0.8) {\tiny 高质量内容};
        \end{tikzpicture>
    \end{center>
\end{frame}

\begin{frame}{问答型提示：自然直观的信息获取}
    \framesubtitle{Q\&A Prompts: Natural and Intuitive Information Retrieval}
    
    \begin{columns}
        \begin{column>{0.5\textwidth}
            \begin{block}{问答型提示的特点}
                \begin{itemize>
                    \item \textbf{自然交互}：符合人类的自然交流习惯
                    \item \textbf{信息导向}：以获取信息为主要目标
                    \item \textbf{精准定位}：能够精确定位所需信息
                    \item \textbf{知识挖掘}：有效挖掘AI的知识储备
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{问题类型分类}
                \begin{itemize>
                    \item \textbf{事实性问题}：询问具体的事实信息
                    \item \textbf{解释性问题}：要求解释概念或现象
                    \item \textbf{分析性问题}：需要分析和推理的问题
                    \item \textbf{建议性问题}：寻求建议和推荐
                \end{itemize>
            \end{block}
            
            \vspace{0.3cm}
            
            \begin{block}{问题设计的技巧}
                \begin{itemize>
                    \item 具体明确：避免过于宽泛的问题
                    \item 层次递进：从基础到深入的问题序列
                    \item 多角度覆盖：从不同角度全面了解主题
                    \item 开放与封闭结合：平衡开放性和具体性
                \end{itemize}
            \end{block>
        \end{column}
        
        \begin{column>{0.5\textwidth}
            \begin{exampleblock}{传媒场景的问答型提示示例}
                \textbf{新闻采访准备}：
                \small
                - Q: 人工智能在新闻业的应用现状如何？
                - Q: 主要的AI新闻工具有哪些？
                - Q: AI对传统记者工作有什么影响？
                - Q: 未来新闻业的AI发展趋势是什么？
                
                \vspace{0.2cm}
                
                \textbf{背景资料搜集}：
                \small
                - Q: 什么是区块链技术的核心原理？
                - Q: 区块链在金融领域有哪些具体应用？
                - Q: 目前区块链技术面临的主要挑战是什么？
                - Q: 如何向普通读者解释区块链的价值？
                
                \vspace{0.2cm}
                
                \textbf{受众分析}：
                \small
                - Q: 90后用户的媒体消费习惯有什么特点？
                - Q: 短视频平台的用户更偏好什么类型的内容？
                - Q: 如何提高内容在社交媒体上的传播效果？
            \end{exampleblock>
            
            \vspace{0.3cm}
            
            \begin{block}{常见问题类型模板}
                \begin{itemize>
                    \item \textbf{定义类}："什么是...？"
                    \item \textbf{原因类}："为什么...？"
                    \item \textbf{方法类}："如何...？"
                    \item \textbf{趋势类}："...的发展趋势如何？"
                    \item \textbf{建议类}："对于...有什么建议？"
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{补全型提示：激发创意的开放式引导}
    \framesubtitle{Completion Prompts: Open-ended Creative Inspiration}
    
    \begin{columns}
        \begin{column>{0.5\textwidth}
            \begin{block}{补全型提示的特点}
                \begin{itemize>
                    \item \textbf{创意激发}：激发AI的创造性思维
                    \item \textbf{自然流畅}：产生自然流畅的内容
                    \item \textbf{灵感触发}：为创作提供灵感和起点
                    \item \textbf{多样性强}：能够产生多样化的结果
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{基本结构模式}
                \begin{itemize>
                    \item \textbf{句子补全}：给出开头，让AI补全
                    \item \textbf{段落延续}：提供段落开头，继续写作
                    \item \textbf{情境设定}：设定情境，让AI发挥
                    \item \textbf{列表扩展}：给出部分列表，要求扩展
                \end{itemize>
            \end{block}
            
            \vspace{0.3cm}
            
            \begin{block>{补全型提示的设计技巧}
                \begin{itemize>
                    \item 方向引导：给出明确的方向指引
                    \item 自然过渡：确保开头与补全内容自然衔接
                    \item 创意空间：留出足够的创意发挥空间
                    \item 质量控制：通过开头设定质量基调
                \end{itemize>
            \end{block>
        \end{column}
        
        \begin{column>{0.5\textwidth}
            \begin{exampleblock}{传媒场景的补全型提示示例}
                \textbf{新闻导语创作}：
                \small
                开头：在人工智能快速发展的今天，\\
                补全：[让AI补全这个新闻导语]
                
                开头：随着5G技术的普及，\\
                补全：[继续写作新闻开头]
                
                \vspace{0.2cm}
                
                \textbf{创意文案生成}：
                \small
                开头：这个夏天，让我们一起...\\
                补全：[为旅游产品创作宣传文案]
                
                开头：当科技遇上传统文化，\\
                补全：[为文化活动创作推广语]
                
                \vspace{0.2cm}
                
                \textbf{故事情节发展}：
                \small
                开头：年轻的记者小李接到一个神秘的线索电话，\\
                补全：[继续发展这个新闻故事]
                
                开头：在这个信息爆炸的时代，\\
                补全：[为纪录片写开场白]
            \end{exampleblock>
            
            \vspace{0.3cm}
            
            \begin{block>{创意激发的高级技巧}
                \begin{itemize>
                    \item 意外元素：在开头加入意外或冲突元素
                    \item 角色视角：从特定角色的视角开始
                    \item 数据引入：用有趣的数据作为开头
                    \item 问题引导：用引人思考的问题开头
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
\end{frame>

\begin{frame}{对话型提示：深入交互的智能对话}
    \framesubtitle{Conversation Prompts: Deep Interactive Dialogue}
    
    \begin{columns}
        \begin{column>{0.5\textwidth}
            \begin{block}{对话型提示的特点}
                \begin{itemize>
                    \item \textbf{互动性强}：支持多轮深入交互
                    \item \textbf{上下文连续}：保持对话的连贯性
                    \item \textbf{逐步深入}：通过对话逐步深入主题
                    \item \textbf{动态调整}：根据回应动态调整策略
                \end{itemize>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{block}{对话设计的核心要素}
                \begin{itemize>
                    \item \textbf{角色设定}：明确对话双方的角色
                    \item \textbf{目标导向}：有明确的对话目标
                    \item \textbf{自然流畅}：保持对话的自然性
                    \item \textbf{信息递进}：信息逐步递进和深化
                \end{itemize>
            \end{block}
            
            \vspace{0.3cm}
            
            \begin{center>
                \begin{tikzpicture}[scale=0.6]
                    % Dialogue flow
                    \node[rectangle,draw,fill=AIBlue] (setup) at (0,2) {开场设定};
                    \node[rectangle,draw,fill=AIGreen] (intro) at (2.5,2) {主题引入};
                    \node[rectangle,draw,fill=AIOrange] (explore) at (5,2) {深入探讨};
                    \node[rectangle,draw,fill=AIRed] (detail) at (2.5,0.5) {细节完善};
                    \node[rectangle,draw,fill=AIGray] (summary) at (0,0.5) {总结确认};
                    \node[rectangle,draw,fill=AILightBlue] (plan) at (5,0.5) {后续计划};
                    
                    \draw[->] (setup) -- (intro);
                    \draw[->] (intro) -- (explore);
                    \draw[->] (explore) -- (detail);
                    \draw[->] (detail) -- (summary);
                    \draw[->] (summary) -- (plan);
                    
                    \node[above] at (2.5,2.8) {\tiny 对话流程};
                \end{tikzpicture>
            \end{center>
        \end{column>
        
        \begin{column>{0.5\textwidth}
            \begin{exampleblock>{传媒场景的对话型提示示例}
                \textbf{模拟采访对话}：
                \small
                设定：你是一位AI技术专家，我是记者，正在采访你关于AI在新闻业的应用。
                
                记者：您好，感谢接受我们的采访。请问您如何看待AI技术在新闻业的发展前景？
                
                专家：[AI回应]
                
                记者：您提到了AI的优势，那么AI在新闻业应用中面临的主要挑战是什么？
                
                专家：[继续对话]
                
                \vspace{0.2cm}
                
                \textbf{内容策划讨论}：
                \small
                设定：我们正在策划一个关于环保的专题报道，你作为资深编辑，帮我完善这个策划。
                
                我：我想做一个关于城市垃圾分类的专题，你觉得从哪个角度切入比较好？
                
                编辑：[AI回应]
                
                我：这个角度很有意思，那我们如何让这个话题更有吸引力？
                
                编辑：[继续讨论]
            \end{exampleblock>
            
            \vspace{0.3cm}
            
            \begin{block>{高质量对话的要素}
                \begin{itemize>
                    \item 逻辑清晰：对话逻辑清晰连贯
                    \item 信息丰富：每轮对话都有信息增量
                    \item 互动自然：对话自然流畅
                    \item 目标达成：最终达成对话目标
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
\end{frame>

\section{第4部分：实践练习}

\begin{frame}{综合实践练习：运用CRISPE框架}
    \framesubtitle{Comprehensive Practice: Applying CRISPE Framework}
    
    \begin{columns}
        \begin{column>{0.5\textwidth}
            \begin{block}{练习一：新闻报道提示词设计}
                \textbf{难度等级}：⭐⭐⭐
                
                \vspace{0.2cm}
                
                \textbf{任务背景}：
                某科技公司发布了新一代AI芯片，声称性能比上一代提升300%，功耗降低50%。你需要为这个新闻事件设计提示词，生成一篇客观、准确的新闻报道。
                
                \vspace{0.2cm}
                
                \textbf{练习要求}：
                \begin{enumerate>
                    \item 使用CRISPE框架设计完整提示词
                    \item 考虑新闻报道的专业要求
                    \item 确保信息的客观性和准确性
                    \item 适合科技媒体发布
                \end{enumerate>
            \end{block>
            
            \vspace{0.3cm}
            
            \begin{exampleblock>{参考框架}
                \textbf{C} - 角色：[请设计] \\
                \textbf{R} - 背景：[请填写] \\
                \textbf{I} - 任务：[请陈述] \\
                \textbf{S} - 风格：[请设定] \\
                \textbf{P} - 实验：[请规划]
            \end{exampleblock>
        \end{column>
        
        \begin{column>{0.5\textwidth}
            \begin{block}{练习二：社交媒体内容创作}
                \textbf{难度等级}：⭐⭐⭐⭐
                
                \vspace{0.2cm}
                
                \textbf{任务背景}：
                为一家环保科技公司的新产品（智能垃圾分类机器人）创作全平台的社交媒体内容，包括微博、微信朋友圈、抖音短视频脚本。
                
                \vspace{0.2cm}
                
                \textbf{挑战要点}：
                \begin{itemize>
                    \item 如何在不同平台保持信息一致性？
                    \item 如何适应不同平台的用户群体？
                    \item 如何设计具有传播力的内容？
                \end{itemize>
            \end{block}
            
            \vspace{0.3cm}
            
            \begin{block}{自我评估清单}
                \begin{itemize>
                    \item 框架完整性：是否包含CRISPE的所有要素？
                    \item 逻辑一致性：各要素之间是否协调统一？
                    \item 具体明确性：描述是否具体明确？
                    \item 可执行性：任务是否可以有效执行？
                    \item 效果导向性：是否以实际效果为目标？
                \end{itemize>
            \end{block>
        \end{column>
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{center>
        \begin{alertblock}{练习步骤}
            1. 需求分析 → 2. 框架选择 → 3. 初步设计 → 4. 自我检查 → 5. 测试优化 → 6. 总结反思
        \end{alertblock>
    \end{center>
\end{frame>

\begin{frame}{第3周总结：掌握提示词工程的艺术}
    \framesubtitle{Week 3 Summary: Mastering the Art of Prompt Engineering}
    
    \begin{columns}[t]
        \begin{column>{0.48\textwidth}
            \begin{block}{本周重点回顾}
                \textbf{1. 提示词的重要性认知}：
                \begin{itemize>
                    \item 连接人类意图与AI理解的桥梁
                    \item 直接影响AI输出的质量和效果
                    \item AI时代传媒人必备的核心技能
                \end{itemize>
                
                \vspace{0.2cm}
                
                \textbf{2. CRISPE框架的系统掌握}：
                \begin{itemize>
                    \item C - 专业、具体的角色设计
                    \item R - 充分、相关的背景信息
                    \item I - 明确、可执行的任务描述
                    \item S - 适配受众和场景的风格
                    \item P - 持续优化的实验思维
                \end{itemize>
            \end{block>
        \end{column>
        
        \begin{column>{0.48\textwidth}
            \begin{block>{关键能力提升}
                \textbf{3. 四种基础模式的灵活运用}：
                \begin{itemize>
                    \item 指令型：直接明确，适合标准化需求
                    \item 问答型：自然直观，适合知识挖掘
                    \item 补全型：创意激发，适合创意生成
                    \item 对话型：深入交互，适合复杂讨论
                \end{itemize>
                
                \vspace{0.2cm}
                
                \textbf{4. 错误识别与预防能力}：
                \begin{itemize>
                    \item 目标模糊、指令矛盾、信息不足
                    \item 标准化、流程化、技能化预防
                    \item 清晰度、完整性、一致性评估
                    \item 系统化的诊断和修复流程
                \end{itemize}
            \end{block>
        \end{column>
    \end{columns>
    
    \vspace{0.3cm}
    
    \begin{exampleblock>{实际应用价值}
        \begin{itemize>
            \item \textbf{新闻写作}：提升新闻内容的质量和效率 \quad 
            \item \textbf{社交媒体}：创作更有吸引力的社交内容 \quad
            \item \textbf{数据分析}：更准确地分析和解读信息 \quad
            \item \textbf{创意策划}：激发更多创新的想法和方案
        \end{itemize>
    \end{exampleblock>
    
    \vspace{0.3cm}
    
    \begin{center>
        \begin{block}{下周预告：第4周 - 精确指令与格式控制}
            \textbf{学习目标}：掌握精确控制AI输出的高级技巧 \\
            \textbf{主要内容}：明确任务指令、上下文信息组织、输出格式精确控制、风格与长度的灵活调节 \\
            \textbf{实践重点}：设计精确的任务指令，掌握多种输出格式控制，学会风格的精准调节
        \end{block>
    \end{center>
\end{frame>

\begin{frame}[plain]
    \begin{center>
        \begin{tikzpicture}[remember picture,overlay]
            \fill[AIBlue] (current page.south west) rectangle (current page.north east);
            \node[white,font=\Huge\bfseries] at (current page.center) {谢谢！};
            \node[white,font=\Large] at ([yshift=-1cm]current page.center) {Thank You!};
            \node[white,font=\large] at ([yshift=-2cm]current page.center) {下周见 See You Next Week};
            
            % Quote
            \node[white,font=\large,text width=8cm,align=center] at ([yshift=-3.5cm]current page.center) {
                "掌握提示词工程，就是掌握了与AI有效沟通的艺术。\\
                这不仅是一项技能，更是通向AI时代成功的钥匙。"
            };
        \end{tikzpicture>
    \end{center>
\end{frame>

\end{document}