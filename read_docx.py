#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取Word文档内容的Python脚本
"""

import sys
import os
from pathlib import Path

def read_docx_with_python_docx():
    """使用python-docx库读取Word文档"""
    try:
        from docx import Document
        
        # 文档路径
        doc_path = "24网传-JOUXXXXX-AI驱动的传媒内容制作大纲.docx"
        
        if not os.path.exists(doc_path):
            print(f"文件不存在: {doc_path}")
            return None
            
        # 读取文档
        doc = Document(doc_path)
        
        # 提取所有段落文本
        full_text = []
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():  # 只添加非空段落
                full_text.append(paragraph.text.strip())
        
        # 提取表格内容
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text.strip())
                if row_text:
                    full_text.append(" | ".join(row_text))
        
        return "\n".join(full_text)
        
    except ImportError:
        print("python-docx库未安装，请运行: pip install python-docx")
        return None
    except Exception as e:
        print(f"读取文档时出错: {e}")
        return None

def read_docx_with_zipfile():
    """使用zipfile和xml解析读取Word文档"""
    try:
        import zipfile
        import xml.etree.ElementTree as ET
        
        doc_path = "24网传-JOUXXXXX-AI驱动的传媒内容制作大纲.docx"
        
        if not os.path.exists(doc_path):
            print(f"文件不存在: {doc_path}")
            return None
        
        # Word文档实际上是一个zip文件
        with zipfile.ZipFile(doc_path, 'r') as zip_file:
            # 读取主文档内容
            xml_content = zip_file.read('word/document.xml')
            
        # 解析XML
        root = ET.fromstring(xml_content)
        
        # 定义命名空间
        namespaces = {
            'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'
        }
        
        # 提取所有文本
        text_elements = root.findall('.//w:t', namespaces)
        full_text = []
        
        for element in text_elements:
            if element.text:
                full_text.append(element.text)
        
        return "".join(full_text)
        
    except Exception as e:
        print(f"使用zipfile方法读取文档时出错: {e}")
        return None

def main():
    """主函数"""
    print("正在尝试读取Word文档...")
    
    # 首先尝试使用python-docx
    content = read_docx_with_python_docx()
    
    if content is None:
        print("尝试使用备用方法...")
        content = read_docx_with_zipfile()
    
    if content:
        print("=" * 50)
        print("文档内容:")
        print("=" * 50)
        print(content)
        print("=" * 50)
        
        # 保存到文本文件
        with open("课程大纲内容.txt", "w", encoding="utf-8") as f:
            f.write(content)
        print("内容已保存到 '课程大纲内容.txt'")
        
    else:
        print("无法读取文档内容")

if __name__ == "__main__":
    main()
