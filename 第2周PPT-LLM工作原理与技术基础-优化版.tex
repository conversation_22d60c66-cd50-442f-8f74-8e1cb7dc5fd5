\documentclass[aspectratio=169,12pt]{beamer}

% 主题设置 - 使用更现代的主题
\usetheme{Boadilla}
\usecolortheme{seahorse}

% 自定义颜色
\definecolor{primaryblue}{RGB}{0,102,204}
\definecolor{secondaryblue}{RGB}{51,153,255}
\definecolor{accentcolor}{RGB}{255,102,0}
\definecolor{textgray}{RGB}{64,64,64}

% 设置主题颜色
\setbeamercolor{structure}{fg=primaryblue}
\setbeamercolor{frametitle}{fg=primaryblue,bg=white}
\setbeamercolor{title}{fg=primaryblue}
\setbeamercolor{item}{fg=secondaryblue}
\setbeamercolor{subitem}{fg=accentcolor}

% 中文支持
\usepackage[UTF8]{ctex}

% 其他包
\usepackage{graphicx}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{tikz}
\usepackage{hyperref}
% \usepackage{enumitem} % 与beamer冲突，移除

% 设置字体大小和行距
\setbeamerfont{normal text}{size=\large}
\setbeamerfont{frametitle}{size=\Large,series=\bfseries}
\setbeamerfont{title}{size=\huge,series=\bfseries}

% 设置列表样式
\setbeamertemplate{itemize items}[circle]
\setbeamertemplate{enumerate items}[default]

% 移除导航栏
\setbeamertemplate{navigation symbols}{}

% 设置页脚
\setbeamertemplate{footline}[frame number]

% 设置代码块样式
\lstset{
    basicstyle=\ttfamily\footnotesize,
    backgroundcolor=\color{gray!10},
    frame=single,
    breaklines=true,
    showstringspaces=false,
    rulecolor=\color{primaryblue}
}

% 自定义命令
\newcommand{\highlight}[1]{\textcolor{accentcolor}{\textbf{#1}}}
\newcommand{\keyword}[1]{\textcolor{primaryblue}{\textbf{#1}}}

% 标题信息
\title{\textbf{LLM工作原理与技术基础}}
\subtitle{第2周课程内容}
\author{AI大模型在传媒领域的应用}
\institute{课程总页数：28页}
\date{\today}

\begin{document}

% 标题页
\begin{frame}
\titlepage
\end{frame}

% 目录页
\begin{frame}{课程大纲}
\tableofcontents
\end{frame}

% 第1部分：回顾与导入
\section{回顾与导入}

\begin{frame}{上周内容回顾}

\begin{block}{\keyword{主要内容回顾}}
\begin{itemize}[leftmargin=1em]
\item \keyword{课程目标}：掌握AI大模型在传媒领域的应用
\item \keyword{AI发展简史}：从达特茅斯会议到大模型时代
\item \keyword{机器学习基础}：监督学习、无监督学习、强化学习
\item \keyword{LLM概述}：大语言模型的定义和特征
\item \keyword{传媒应用}：AI在传媒各环节的应用潜力
\end{itemize}
\end{block}

\begin{block}{\keyword{关键概念}}
\begin{itemize}[leftmargin=1em]
\item 人工智能的发展历程
\item 机器学习的三种类型
\item 深度学习与神经网络
\item 大语言模型的突破性意义
\end{itemize}
\end{block}

\begin{alertblock}{\keyword{思考问题}}
\begin{itemize}[leftmargin=1em]
\item AI技术如何改变传媒行业？
\item 大语言模型与传统AI有什么区别？
\end{itemize}
\end{alertblock}

\end{frame}

\begin{frame}{本周学习目标}

\begin{block}{\keyword{学习目标}}
\begin{itemize}[leftmargin=1em]
\item \highlight{了解Transformer架构}的基本原理和注意力机制
\item \highlight{理解Tokenization概念}及其对模型的影响
\item \highlight{掌握LLM的训练过程}：预训练、微调、RLHF
\item \highlight{认识LLM的能力边界}与局限性
\end{itemize}
\end{block}

\begin{block}{\keyword{内容安排}}
\begin{enumerate}[leftmargin=1em]
\item \textbf{Transformer架构简介}（10页）
\item \textbf{Tokenization概念}（4页）
\item \textbf{LLM训练过程}（8页）
\item \textbf{能力边界分析}（4页）
\end{enumerate}
\end{block}

\begin{alertblock}{\keyword{重点难点}}
\begin{itemize}[leftmargin=1em]
\item 注意力机制的理解
\item 训练过程的复杂性
\item 能力边界的准确认知
\end{itemize}
\end{alertblock}

\end{frame}

% 第2部分：Transformer架构
\section{Transformer架构}

\begin{frame}{为什么需要Transformer？}
\frametitle{从RNN到Transformer：架构演进的必然}

\begin{columns}[T]
\begin{column}{0.5\textwidth}
\begin{block}{\keyword{传统RNN的局限性}}
\begin{itemize}[leftmargin=1em]
\item \highlight{序列处理}：必须按顺序处理，无法并行
\item \highlight{长距离依赖}：难以捕捉长序列中的远距离关系
\item \highlight{梯度问题}：梯度消失和梯度爆炸
\item \highlight{训练效率}：训练时间长，计算效率低
\end{itemize}
\end{block}

\begin{block}{\keyword{LSTM的改进与不足}}
\begin{itemize}[leftmargin=1em]
\item \textcolor{green}{\textbf{改进}}：通过门控机制缓解梯度问题
\item \textcolor{red}{\textbf{不足}}：序列处理限制依然存在
\end{itemize}
\end{block}
\end{column}

\begin{column}{0.5\textwidth}
\begin{block}{\keyword{Transformer的突破}}
\begin{itemize}[leftmargin=1em]
\item \highlight{并行处理}：所有位置可以同时计算
\item \highlight{长距离依赖}：直接建模任意位置间的关系
\item \highlight{训练效率}：大幅提升训练速度
\item \highlight{简洁优雅}：架构相对简单，易于理解
\end{itemize}
\end{block}

\begin{alertblock}{\keyword{影响深远}}
\begin{itemize}[leftmargin=1em]
\item 在多个NLP任务上取得突破
\item 成为现代大模型的基础架构
\item 从NLP扩展到计算机视觉等领域
\end{itemize}
\end{alertblock}
\end{column}
\end{columns}

\end{frame}

\begin{frame}{注意力机制的直观理解}
\frametitle{注意力机制：模拟人类的注意力}

\begin{columns}[T]
\begin{column}{0.5\textwidth}
\begin{block}{\keyword{人类注意力的特点}}
\begin{itemize}[leftmargin=1em]
\item \highlight{选择性关注}：在复杂环境中聚焦重要信息
\item \highlight{动态调整}：根据任务需求调整注意力分配
\item \highlight{并行处理}：同时处理多个信息源
\item \highlight{上下文相关}：基于上下文决定关注重点
\end{itemize}
\end{block}

\begin{block}{\keyword{机器注意力机制}}
\begin{itemize}[leftmargin=1em]
\item \highlight{权重分配}：为不同位置分配不同的注意力权重
\item \highlight{相关性计算}：计算查询与键之间的相关性
\item \highlight{加权求和}：基于权重对值进行加权平均
\item \highlight{动态聚焦}：根据输入动态调整关注重点
\end{itemize}
\end{block}
\end{column}

\begin{column}{0.5\textwidth}
\begin{block}{\keyword{注意力机制的优势}}
\begin{itemize}[leftmargin=1em]
\item \highlight{全局视野}：能够同时关注序列中的所有位置
\item \highlight{精准定位}：准确识别重要信息的位置
\item \highlight{灵活适应}：根据不同任务调整注意力模式
\item \highlight{可解释性}：注意力权重提供模型决策的可视化
\end{itemize}
\end{block}

\begin{exampleblock}{\keyword{生活中的类比}}
\begin{itemize}[leftmargin=1em]
\item \textbf{阅读理解}：关注关键词和重要句子
\item \textbf{听音乐}：在复杂音乐中聚焦主旋律
\item \textbf{开车}：同时关注路况、信号灯、行人
\item \textbf{对话}：在嘈杂环境中专注于对话者
\end{itemize}
\end{exampleblock}
\end{column}
\end{columns}

\end{frame}

\begin{frame}{Self-Attention机制详解}
\frametitle{Self-Attention：序列内部的关系建模}

\begin{block}{\keyword{Self-Attention的核心思想}}
\begin{itemize}[leftmargin=1em]
\item \highlight{自我关注}：序列中每个位置关注其他所有位置
\item \highlight{全局连接}：直接建模任意两个位置间的关系
\item \highlight{权重计算}：动态计算每个位置的重要性权重
\item \highlight{信息整合}：基于权重整合全局信息
\end{itemize}
\end{block}

\begin{columns}[T]
\begin{column}{0.5\textwidth}
\begin{block}{\keyword{三个关键概念}}
\begin{itemize}[leftmargin=1em]
\item \highlight{Query（查询）}：当前位置想要查找的信息
\item \highlight{Key（键）}：每个位置提供的索引信息
\item \highlight{Value（值）}：每个位置包含的实际内容
\end{itemize}
\end{block}

\begin{block}{\keyword{计算过程}}
\begin{enumerate}[leftmargin=1em]
\item 计算相似度：Attention Score = Query × Key\textsuperscript{T}
\item 归一化权重：Attention Weight = Softmax(Score)
\item 加权求和：Output = Attention Weight × Value
\end{enumerate}
\end{block}
\end{column}

\begin{column}{0.5\textwidth}
\begin{alertblock}{\keyword{数学公式}}
$$\text{Attention}(Q,K,V) = \text{Softmax}\left(\frac{QK^T}{\sqrt{d_k}}\right)V$$
\end{alertblock}

\begin{block}{\keyword{优势特点}}
\begin{itemize}[leftmargin=1em]
\item \highlight{并行计算}：所有位置可以同时计算
\item \highlight{长距离建模}：直接连接远距离位置
\item \highlight{动态权重}：根据输入内容动态调整
\item \highlight{可解释性}：注意力权重可视化
\end{itemize}
\end{block}
\end{column}
\end{columns}

\end{frame}

\begin{frame}{多头注意力机制}
\frametitle{Multi-Head Attention：多角度的信息捕捉}

\begin{columns}[T]
\begin{column}{0.5\textwidth}
\begin{block}{\keyword{为什么需要多头注意力？}}
\begin{itemize}[leftmargin=1em]
\item \highlight{多样化关注}：从不同角度关注信息
\item \highlight{丰富表示}：捕捉更丰富的语义关系
\item \highlight{并行处理}：多个注意力头并行工作
\item \highlight{性能提升}：显著提升模型表现
\end{itemize}
\end{block}

\begin{block}{\keyword{工作原理}}
\begin{enumerate}[leftmargin=1em]
\item \textbf{线性变换}：将输入投影到多个子空间
\item \textbf{并行计算}：每个头独立计算注意力
\item \textbf{结果拼接}：将所有头的输出拼接
\item \textbf{最终投影}：通过线性层得到最终输出
\end{enumerate}
\end{block}
\end{column}

\begin{column}{0.5\textwidth}
\begin{alertblock}{\keyword{数学表示}}
\begin{align}
\text{MultiHead}(Q,K,V) &= \text{Concat}(\text{head}_1, \ldots, \text{head}_h)W^O \\
\text{其中 head}_i &= \text{Attention}(QW_i^Q, KW_i^K, VW_i^V)
\end{align}
\end{alertblock}

\begin{exampleblock}{\keyword{不同头的作用}}
\begin{itemize}[leftmargin=1em]
\item \textbf{语法关系}：某些头专注于语法结构
\item \textbf{语义关系}：某些头关注语义相关性
\item \textbf{位置关系}：某些头捕捉位置信息
\item \textbf{长距离依赖}：某些头建模远距离关系
\end{itemize}
\end{exampleblock}
\end{column}
\end{columns}

\end{frame}

% 第3部分：Tokenization
\section{Tokenization}

\begin{frame}{什么是Token？}
\frametitle{Token：AI理解语言的基本单位}

\begin{columns}[T]
\begin{column}{0.5\textwidth}
\begin{block}{\keyword{Token的定义}}
\begin{itemize}[leftmargin=1em]
\item \highlight{基本单位}：AI模型处理文本的最小单位
\item \highlight{数字表示}：将文本转换为数字序列
\item \highlight{统一格式}：不同语言和符号的统一表示
\item \highlight{模型输入}：神经网络的实际输入格式
\end{itemize}
\end{block}

\begin{block}{\keyword{为什么需要Tokenization？}}
\begin{itemize}[leftmargin=1em]
\item \highlight{机器理解}：计算机只能处理数字
\item \highlight{统一处理}：将不同类型的文本统一为数字序列
\item \highlight{效率考虑}：合适的分词能提高处理效率
\item \highlight{语义保持}：尽可能保持原文的语义信息
\end{itemize}
\end{block}
\end{column}

\begin{column}{0.5\textwidth}
\begin{block}{\keyword{Token的类型}}
\begin{itemize}[leftmargin=1em]
\item \highlight{字符级}：每个字符是一个Token
\item \highlight{词级}：每个单词是一个Token
\item \highlight{子词级}：介于字符和单词之间
\item \highlight{句子级}：整个句子作为一个Token
\end{itemize}
\end{block}

\begin{exampleblock}{\keyword{实际例子}}
\textbf{原文}："Hello, world!"
\begin{itemize}[leftmargin=1em]
\item \textbf{字符级}：["H", "e", "l", "l", "o", ",", " ", "w", "o", "r", "l", "d", "!"]
\item \textbf{词级}：["Hello", ",", "world", "!"]
\item \textbf{子词级}：["Hello", ",", "world", "!"]
\end{itemize}
\end{exampleblock}
\end{column}
\end{columns}

\end{frame}

% 第4部分：LLM训练过程
\section{LLM训练过程}

\begin{frame}{LLM训练概览}
\frametitle{LLM训练：从数据到智能的转化}

\begin{alertblock}{\keyword{训练阶段概览}}
\begin{center}
\Large
原始数据 → 数据预处理 → 预训练 → 微调 → RLHF → 部署应用
\end{center}
\end{alertblock}

\begin{block}{\keyword{三个主要阶段}}
\begin{enumerate}[leftmargin=1em]
\item \textbf{预训练（Pre-training）}
   \begin{itemize}[leftmargin=2em]
   \item \highlight{目标}：学习语言的基本规律和知识
   \item \highlight{数据}：大规模无标注文本数据
   \item \highlight{方法}：自监督学习，预测下一个词
   \item \highlight{时间}：数周到数月
   \end{itemize}

\item \textbf{微调（Fine-tuning）}
   \begin{itemize}[leftmargin=2em]
   \item \highlight{目标}：适应特定任务或领域
   \item \highlight{数据}：少量高质量标注数据
   \item \highlight{方法}：有监督学习，任务特定优化
   \item \highlight{时间}：数小时到数天
   \end{itemize}

\item \textbf{人类反馈强化学习（RLHF）}
   \begin{itemize}[leftmargin=2em]
   \item \highlight{目标}：对齐人类价值观和偏好
   \item \highlight{数据}：人类标注的偏好数据
   \item \highlight{方法}：强化学习，奖励模型指导
   \item \highlight{时间}：数天到数周
   \end{itemize}
\end{enumerate}
\end{block}

\end{frame}

% 第5部分：能力边界分析
\section{能力边界分析}

\begin{frame}{LLM的核心优势}
\frametitle{LLM的超能力：理解、生成、推理}

\begin{columns}[T]
\begin{column}{0.5\textwidth}
\begin{block}{\keyword{强大的语言理解能力}}
\begin{itemize}[leftmargin=1em]
\item \highlight{深度理解}：理解文本的深层含义和隐含信息
\item \highlight{上下文感知}：基于长上下文进行准确理解
\item \highlight{语义推理}：进行复杂的语义推理和关联
\item \highlight{意图识别}：准确识别用户的真实意图
\end{itemize}
\end{block}

\begin{block}{\keyword{卓越的内容生成能力}}
\begin{itemize}[leftmargin=1em]
\item \highlight{多样化写作}：适应不同风格、体裁、受众
\item \highlight{创意生成}：产生新颖的想法和创意内容
\item \highlight{格式适配}：生成各种格式和结构的内容
\item \highlight{结构化输出}：按要求组织信息结构
\end{itemize}
\end{block}
\end{column}

\begin{column}{0.5\textwidth}
\begin{block}{\keyword{出色的推理能力}}
\begin{itemize}[leftmargin=1em]
\item \highlight{逻辑推理}：进行复杂的逻辑分析和推理
\item \highlight{因果关系}：理解和分析因果关系
\item \highlight{数据分析}：从数据中提取洞察和结论
\item \highlight{问题解决}：分解和解决复杂问题
\end{itemize}
\end{block}

\begin{exampleblock}{\keyword{在传媒中的优势体现}}
\begin{itemize}[leftmargin=1em]
\item \textbf{内容创作}：高质量的新闻、文章、脚本创作
\item \textbf{信息分析}：深度的文本分析和信息提取
\item \textbf{用户交互}：自然流畅的用户对话和服务
\item \textbf{个性化服务}：基于用户需求的定制化内容
\end{itemize}
\end{exampleblock}
\end{column}
\end{columns}

\end{frame}

\begin{frame}{LLM的局限性与"幻觉"现象}
\frametitle{理性认识：LLM的局限性与挑战}

\begin{columns}[T]
\begin{column}{0.5\textwidth}
\begin{alertblock}{\keyword{"幻觉"现象详解}}
\textbf{定义}：模型生成看似合理但实际错误的信息

\textbf{表现形式}：
\begin{itemize}[leftmargin=1em]
\item 编造不存在的事实
\item 虚构人名、地名、数据
\item 创造虚假的引用和来源
\item 生成错误的数学计算
\end{itemize}
\end{alertblock}

\begin{block}{\keyword{其他主要局限性}}
\begin{itemize}[leftmargin=1em]
\item \highlight{知识截止}：无法获取训练后的最新信息
\item \highlight{数学计算}：在复杂数学运算上的不足
\item \highlight{事实核查}：无法实时验证信息的准确性
\item \highlight{记忆限制}：上下文长度的物理限制
\end{itemize}
\end{block}
\end{column}

\begin{column}{0.5\textwidth}
\begin{block}{\keyword{在传媒应用中的风险}}
\begin{itemize}[leftmargin=1em]
\item \highlight{新闻准确性}：可能生成虚假新闻信息
\item \highlight{数据错误}：统计数据和事实的错误
\item \highlight{人物信息}：关于真实人物的错误信息
\item \highlight{事件描述}：对历史事件的错误描述
\end{itemize}
\end{block}

\begin{exampleblock}{\keyword{应对策略}}
\begin{itemize}[leftmargin=1em]
\item \textbf{人工审核}：建立严格的人工审核机制
\item \textbf{多源验证}：交叉验证重要信息
\item \textbf{知识库辅助}：结合可靠的知识库
\item \textbf{风险提示}：向用户明确AI的局限性
\end{itemize}
\end{exampleblock}
\end{column}
\end{columns}

\end{frame}

\begin{frame}{课程总结与下周预告}
\frametitle{第2周总结：深入理解LLM的技术基础}

\begin{block}{\keyword{本周重点回顾}}
\begin{itemize}[leftmargin=1em]
\item \textbf{Transformer架构}：注意力机制的工作原理、多头注意力的优势、位置编码的重要性
\item \textbf{Tokenization技术}：Token的概念和重要性、不同分词方法的对比、BPE算法的原理
\item \textbf{LLM训练过程}：预训练的核心思想、微调技术的应用、RLHF的重要作用
\item \textbf{能力边界认知}：LLM的核心优势、局限性和"幻觉"现象、正确使用的原则
\end{itemize}
\end{block}

\begin{alertblock}{\keyword{关键概念掌握}}
\begin{itemize}[leftmargin=1em]
\item \highlight{Self-Attention}：序列内部关系建模的核心机制
\item \highlight{预训练+微调}：现代LLM的标准训练范式
\item \highlight{指令微调}：让AI理解和遵循自然语言指令
\item \highlight{RLHF}：让AI与人类价值观对齐的关键技术
\item \highlight{幻觉现象}：AI生成错误信息的重要风险
\end{itemize}
\end{alertblock}

\begin{exampleblock}{\keyword{下周预告：第3周 - 提示词工程基础}}
\begin{itemize}[leftmargin=1em]
\item \textbf{学习目标}：掌握与AI有效沟通的艺术
\item \textbf{主要内容}：提示词的核心地位、CRISPE框架、四种基础提示模式、常见错误分析
\item \textbf{实践重点}：设计有效的提示词、避免常见错误、针对传媒场景的提示词优化
\end{itemize}
\end{exampleblock}

\end{frame}

\end{document}
