\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{1}{1/1}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {1}{1}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{2}{2/2}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {2}{2}}}
\@writefile{toc}{\beamer@sectionintoc {1}{回顾与导入}{3}{0}{1}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {1}{2}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {1}{2}}}
\@writefile{nav}{\headcommand {\sectionentry {1}{回顾与导入}{3}{回顾与导入}{0}}}
\@writefile{nav}{\headcommand {\slideentry {1}{0}{1}{3/3}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {3}{3}}}
\@writefile{nav}{\headcommand {\slideentry {1}{0}{2}{4/4}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {4}{4}}}
\@writefile{toc}{\beamer@sectionintoc {2}{Transformer架构}{5}{0}{2}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {3}{4}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {3}{4}}}
\@writefile{nav}{\headcommand {\sectionentry {2}{Transformer架构}{5}{Transformer架构}{0}}}
\@writefile{nav}{\headcommand {\slideentry {2}{0}{1}{5/5}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {5}{5}}}
\@writefile{nav}{\headcommand {\slideentry {2}{0}{2}{6/6}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {6}{6}}}
\@writefile{nav}{\headcommand {\slideentry {2}{0}{3}{7/7}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {7}{7}}}
\@writefile{nav}{\headcommand {\slideentry {2}{0}{4}{8/8}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {8}{8}}}
\@writefile{toc}{\beamer@sectionintoc {3}{Tokenization}{9}{0}{3}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {5}{8}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {5}{8}}}
\@writefile{nav}{\headcommand {\sectionentry {3}{Tokenization}{9}{Tokenization}{0}}}
\@writefile{nav}{\headcommand {\slideentry {3}{0}{1}{9/9}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {9}{9}}}
\@writefile{toc}{\beamer@sectionintoc {4}{LLM训练过程}{10}{0}{4}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {9}{9}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {9}{9}}}
\@writefile{nav}{\headcommand {\sectionentry {4}{LLM训练过程}{10}{LLM训练过程}{0}}}
\@writefile{nav}{\headcommand {\slideentry {4}{0}{1}{10/10}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {10}{10}}}
\@writefile{toc}{\beamer@sectionintoc {5}{能力边界分析}{11}{0}{5}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {10}{10}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {10}{10}}}
\@writefile{nav}{\headcommand {\sectionentry {5}{能力边界分析}{11}{能力边界分析}{0}}}
\@writefile{nav}{\headcommand {\slideentry {5}{0}{1}{11/11}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {11}{11}}}
\@writefile{nav}{\headcommand {\slideentry {5}{0}{2}{12/12}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {12}{12}}}
\@writefile{nav}{\headcommand {\slideentry {5}{0}{3}{13/13}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {13}{13}}}
\@writefile{nav}{\headcommand {\beamer@partpages {1}{13}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {11}{13}}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {11}{13}}}
\@writefile{nav}{\headcommand {\beamer@documentpages {13}}}
\@writefile{nav}{\headcommand {\gdef \inserttotalframenumber {13}}}
\gdef \@abspage@last{13}
