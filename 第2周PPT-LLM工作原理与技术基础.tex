\documentclass[aspectratio=169]{beamer}

% 主题设置
\usetheme{Madrid}
\usecolortheme{default}

% 中文支持
\usepackage[UTF8]{ctex}

% 其他包
\usepackage{graphicx}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{tikz}
\usepackage{hyperref}

% 设置代码块样式
\lstset{
    basicstyle=\ttfamily\small,
    backgroundcolor=\color{gray!10},
    frame=single,
    breaklines=true,
    showstringspaces=false
}

% 标题信息
\title{第2周：LLM工作原理与技术基础}
\subtitle{总页数：28页}
\author{AI大模型在传媒领域的应用}
\date{\today}

\begin{document}

% 标题页
\begin{frame}
\titlepage
\end{frame}

% 目录页
\begin{frame}{目录}
\tableofcontents
\end{frame}

% 第1部分：回顾与导入
\section{回顾与导入}

\begin{frame}{第1页：上周回顾}
\frametitle{第1周内容回顾}

\textbf{主要内容回顾：}
\begin{itemize}
\item \textbf{课程目标}：掌握AI大模型在传媒领域的应用
\item \textbf{AI发展简史}：从达特茅斯会议到大模型时代
\item \textbf{机器学习基础}：监督学习、无监督学习、强化学习
\item \textbf{LLM概述}：大语言模型的定义和特征
\item \textbf{传媒应用}：AI在传媒各环节的应用潜力
\end{itemize}

\textbf{关键概念：}
\begin{itemize}
\item 人工智能的发展历程
\item 机器学习的三种类型
\item 深度学习与神经网络
\item 大语言模型的突破性意义
\item 数据驱动的学习范式
\end{itemize}

\textbf{思考问题：}
\begin{itemize}
\item AI技术如何改变传媒行业？
\item 大语言模型与传统AI有什么区别？
\item 传媒人如何适应AI时代？
\end{itemize}
\end{frame}

\begin{frame}{第2页：本周学习目标}
\frametitle{第2周学习目标与内容预览}

\textbf{学习目标：}
\begin{itemize}
\item 🎯 \textbf{了解Transformer架构}的基本原理和注意力机制
\item 🎯 \textbf{理解Tokenization概念}及其对模型的影响
\item 🎯 \textbf{掌握LLM的训练过程}：预训练、微调、RLHF
\item 🎯 \textbf{认识LLM的能力边界}与局限性
\end{itemize}

\textbf{内容安排：}
\begin{enumerate}
\item \textbf{Transformer架构简介}（10页）
   \begin{itemize}
   \item 注意力机制的直观理解
   \item Self-Attention工作原理
   \item Encoder-Decoder结构
   \end{itemize}

\item \textbf{Tokenization概念}（4页）
   \begin{itemize}
   \item 什么是Token
   \item 分词方法对比
   \item Token对模型理解的影响
   \end{itemize}

\item \textbf{LLM训练过程}（8页）
   \begin{itemize}
   \item 预训练阶段详解
   \item 微调技术介绍
   \item RLHF人类反馈强化学习
   \end{itemize}

\item \textbf{能力边界分析}（4页）
   \begin{itemize}
   \item LLM的优势与局限
   \item "幻觉"现象解析
   \item 实际应用考虑
   \end{itemize}
\end{enumerate}

\textbf{重点难点：}
\begin{itemize}
\item ⚠️ 注意力机制的理解
\item ⚠️ 训练过程的复杂性
\item ⚠️ 能力边界的准确认知
\end{itemize}
\end{frame}

% 第2部分：Transformer架构
\section{Transformer架构}

\begin{frame}{第3页：为什么需要Transformer？}
\frametitle{从RNN到Transformer：架构演进的必然}

\textbf{传统RNN的局限性：}
\begin{itemize}
\item 🐌 \textbf{序列处理}：必须按顺序处理，无法并行
\item 📉 \textbf{长距离依赖}：难以捕捉长序列中的远距离关系
\item 💾 \textbf{梯度问题}：梯度消失和梯度爆炸
\item ⏱️ \textbf{训练效率}：训练时间长，计算效率低
\end{itemize}

\textbf{LSTM的改进与不足：}
\begin{itemize}
\item ✅ \textbf{改进}：通过门控机制缓解梯度问题
\item ❌ \textbf{仍存在问题}：序列处理限制依然存在
\item ❌ \textbf{复杂性}：结构复杂，难以优化
\end{itemize}

\textbf{Transformer的突破：}
\begin{itemize}
\item ⚡ \textbf{并行处理}：所有位置可以同时计算
\item 🎯 \textbf{长距离依赖}：直接建模任意位置间的关系
\item 🚀 \textbf{训练效率}：大幅提升训练速度
\item 🎨 \textbf{简洁优雅}：架构相对简单，易于理解和实现
\end{itemize}

\textbf{影响深远：}
\begin{itemize}
\item 📈 \textbf{性能提升}：在多个NLP任务上取得突破
\item 🏗️ \textbf{架构基础}：成为现代大模型的基础架构
\item 🌍 \textbf{广泛应用}：从NLP扩展到计算机视觉等领域
\end{itemize}
\end{frame}

\begin{frame}{第4页：注意力机制的直观理解}
\frametitle{注意力机制：模拟人类的注意力}

\textbf{人类注意力的特点：}
\begin{itemize}
\item 👁️ \textbf{选择性关注}：在复杂环境中聚焦重要信息
\item 🎯 \textbf{动态调整}：根据任务需求调整注意力分配
\item 🧠 \textbf{并行处理}：同时处理多个信息源
\item 💡 \textbf{上下文相关}：基于上下文决定关注重点
\end{itemize}

\textbf{机器注意力机制：}
\begin{itemize}
\item 📊 \textbf{权重分配}：为不同位置分配不同的注意力权重
\item 🔍 \textbf{相关性计算}：计算查询与键之间的相关性
\item 📈 \textbf{加权求和}：基于权重对值进行加权平均
\item 🎯 \textbf{动态聚焦}：根据输入动态调整关注重点
\end{itemize}

\textbf{注意力机制的优势：}
\begin{itemize}
\item 🌐 \textbf{全局视野}：能够同时关注序列中的所有位置
\item 🎯 \textbf{精准定位}：准确识别重要信息的位置
\item 🔄 \textbf{灵活适应}：根据不同任务调整注意力模式
\item 📊 \textbf{可解释性}：注意力权重提供模型决策的可视化
\end{itemize}

\textbf{生活中的类比：}
\begin{itemize}
\item 📚 \textbf{阅读理解}：在阅读时关注关键词和重要句子
\item 🎵 \textbf{听音乐}：在复杂音乐中聚焦主旋律
\item 🚗 \textbf{开车}：同时关注路况、信号灯、行人等
\item 👥 \textbf{对话}：在嘈杂环境中专注于对话者的声音
\end{itemize}
\end{frame}

\begin{frame}{第6页：多头注意力机制}
\frametitle{Multi-Head Attention：多角度的信息捕捉}

\textbf{为什么需要多头注意力？}
\begin{itemize}
\item 🎯 \textbf{多样化关注}：从不同角度关注信息
\item 🧠 \textbf{丰富表示}：捕捉更丰富的语义关系
\item 🔄 \textbf{并行处理}：多个注意力头并行工作
\item 📊 \textbf{性能提升}：显著提升模型表现
\end{itemize}

\textbf{多头注意力的工作原理：}
\begin{enumerate}
\item \textbf{线性变换}：将输入投影到多个子空间
\item \textbf{并行计算}：每个头独立计算注意力
\item \textbf{结果拼接}：将所有头的输出拼接
\item \textbf{最终投影}：通过线性层得到最终输出
\end{enumerate}

\textbf{数学表示：}
\begin{align}
\text{MultiHead}(Q,K,V) &= \text{Concat}(\text{head}_1, \ldots, \text{head}_h)W^O \\
\text{其中 head}_i &= \text{Attention}(QW_i^Q, KW_i^K, VW_i^V)
\end{align}

\textbf{不同头的作用：}
\begin{itemize}
\item 🎯 \textbf{语法关系}：某些头专注于语法结构
\item 💭 \textbf{语义关系}：某些头关注语义相关性
\item 📍 \textbf{位置关系}：某些头捕捉位置信息
\item 🔗 \textbf{长距离依赖}：某些头建模远距离关系
\end{itemize}

\textbf{类比理解：}
\begin{itemize}
\item 👥 \textbf{团队协作}：不同专家从不同角度分析问题
\item 🎨 \textbf{多角度观察}：从多个视角观察同一个物体
\item 📊 \textbf{多维分析}：从多个维度分析数据
\item 🔍 \textbf{多重检查}：多次检查确保准确性
\end{itemize}

\textbf{实际效果：}
\begin{itemize}
\item 📈 \textbf{性能提升}：相比单头注意力显著提升
\item 🎯 \textbf{专业化}：不同头学会专注不同类型的关系
\item 🔄 \textbf{鲁棒性}：多头机制提供冗余和鲁棒性
\item 📊 \textbf{可解释性}：可以分析不同头的关注模式
\end{itemize}
\end{frame}

\begin{frame}{第7页：位置编码的重要性}
\frametitle{位置编码：为Transformer注入序列信息}

\textbf{位置信息的重要性：}
\begin{itemize}
\item 📍 \textbf{序列顺序}：语言中词序对意义至关重要
\item 🔄 \textbf{时间关系}：事件的时间顺序影响理解
\item 📊 \textbf{结构信息}：句法结构依赖位置关系
\item 🎯 \textbf{语义区分}：相同词在不同位置意义不同
\end{itemize}

\textbf{Transformer的位置编码挑战：}
\begin{itemize}
\item ❌ \textbf{天然无序}：Self-Attention机制本身不考虑位置
\item 🔄 \textbf{置换不变性}：打乱输入顺序结果相同
\item 📊 \textbf{需要补充}：必须显式添加位置信息
\item 🎯 \textbf{编码方式}：如何有效编码位置信息
\end{itemize}

\textbf{位置编码的设计原则：}
\begin{itemize}
\item 📏 \textbf{唯一性}：每个位置有唯一的编码
\item 🔄 \textbf{相对关系}：能够表示位置间的相对关系
\item 📊 \textbf{可扩展性}：能够处理不同长度的序列
\item ⚡ \textbf{计算效率}：编码计算要高效
\end{itemize}

\textbf{正弦位置编码：}
\begin{align}
PE(pos, 2i) &= \sin(pos/10000^{2i/d_{model}}) \\
PE(pos, 2i+1) &= \cos(pos/10000^{2i/d_{model}})
\end{align}

\textbf{正弦编码的优势：}
\begin{itemize}
\item 🌊 \textbf{周期性}：不同频率的正弦波组合
\item 📏 \textbf{相对位置}：能够表示相对位置关系
\item 🔄 \textbf{外推能力}：可以处理训练时未见过的长度
\item ⚡ \textbf{计算简单}：无需学习参数，直接计算
\end{itemize}

\textbf{其他位置编码方法：}
\begin{itemize}
\item 📚 \textbf{学习式编码}：通过训练学习位置表示
\item 🔄 \textbf{相对位置编码}：直接建模相对位置关系
\item 🎯 \textbf{旋转位置编码（RoPE）}：通过旋转操作编码位置
\end{itemize}
\end{frame}

\begin{frame}{第8页：Transformer的完整架构}
\frametitle{Transformer架构全貌}

\textbf{整体架构图：}
\begin{lstlisting}
输入 → 嵌入层 → 位置编码 →
Encoder层 × N → Decoder层 × N →
线性层 → Softmax → 输出
\end{lstlisting}

\textbf{Encoder层结构：}
\begin{itemize}
\item 🔍 \textbf{Multi-Head Attention}：多头自注意力机制
\item ➕ \textbf{残差连接}：Add \& Norm操作
\item 🧠 \textbf{Feed Forward}：前馈神经网络
\item ➕ \textbf{残差连接}：Add \& Norm操作
\end{itemize}

\textbf{Decoder层结构：}
\begin{itemize}
\item 🎭 \textbf{Masked Multi-Head Attention}：掩码自注意力
\item ➕ \textbf{残差连接}：Add \& Norm操作
\item 🔗 \textbf{Cross Attention}：编码器-解码器注意力
\item ➕ \textbf{残差连接}：Add \& Norm操作
\item 🧠 \textbf{Feed Forward}：前馈神经网络
\item ➕ \textbf{残差连接}：Add \& Norm操作
\end{itemize}

\textbf{关键组件详解：}
\begin{itemize}
\item 🔄 \textbf{残差连接}：缓解深层网络的梯度消失问题
\item 📊 \textbf{层归一化}：稳定训练过程，加速收敛
\item 🧠 \textbf{前馈网络}：增加模型的非线性表达能力
\item 🎭 \textbf{掩码机制}：防止解码器看到未来信息
\end{itemize}

\textbf{训练技巧：}
\begin{itemize}
\item 📚 \textbf{Teacher Forcing}：训练时使用真实标签
\item 🎯 \textbf{标签平滑}：减少过拟合，提高泛化能力
\item 🔄 \textbf{Dropout}：随机丢弃神经元，防止过拟合
\item 📈 \textbf{学习率调度}：动态调整学习率
\end{itemize}
\end{frame}

\begin{frame}{第9页：Encoder-Decoder vs Decoder-Only}
\frametitle{两种主流架构：各有所长}

\textbf{Encoder-Decoder架构：}
\begin{itemize}
\item 🏗️ \textbf{结构特点}：编码器+解码器的双塔结构
\item 🎯 \textbf{适用任务}：序列到序列的转换任务
\item 💡 \textbf{工作原理}：编码器理解输入，解码器生成输出
\item 📊 \textbf{代表模型}：T5、BART、机器翻译模型
\end{itemize}

\textbf{Encoder-Decoder的优势：}
\begin{itemize}
\item 🎯 \textbf{任务专门化}：编码和解码功能分离
\item 🔄 \textbf{双向理解}：编码器可以双向理解输入
\item 📊 \textbf{结构清晰}：输入输出处理逻辑清晰
\item 🎨 \textbf{灵活性}：可以处理不同长度的输入输出
\end{itemize}

\textbf{Decoder-Only架构：}
\begin{itemize}
\item 🏗️ \textbf{结构特点}：只有解码器的单塔结构
\item 🎯 \textbf{适用任务}：自回归文本生成
\item 💡 \textbf{工作原理}：逐词预测下一个词
\item 📊 \textbf{代表模型}：GPT系列、LLaMA、PaLM
\end{itemize}

\textbf{Decoder-Only的优势：}
\begin{itemize}
\item ⚡ \textbf{训练效率}：结构简单，训练更高效
\item 🎯 \textbf{生成能力}：专门优化文本生成任务
\item 📈 \textbf{可扩展性}：更容易扩展到大规模
\item 🔄 \textbf{统一框架}：用一个框架处理多种任务
\end{itemize}

\textbf{架构选择考虑：}
\begin{itemize}
\item 🎯 \textbf{任务类型}：生成任务选Decoder-Only，转换任务选Encoder-Decoder
\item 📊 \textbf{数据特点}：考虑输入输出的长度和复杂度
\item ⚡ \textbf{计算资源}：Decoder-Only通常更高效
\item 🎨 \textbf{应用场景}：根据具体应用需求选择
\end{itemize}

\textbf{发展趋势：}
\begin{itemize}
\item 📈 \textbf{Decoder-Only主导}：大模型时代的主流选择
\item 🔄 \textbf{架构融合}：结合两种架构的优势
\item 🎯 \textbf{任务适配}：针对特定任务优化架构
\item 🚀 \textbf{持续创新}：新的架构变体不断涌现
\end{itemize}
\end{frame}

\begin{frame}{第10页：Transformer的优势与影响}
\frametitle{Transformer：改变AI的架构革命}

\textbf{技术优势总结：}
\begin{itemize}
\item ⚡ \textbf{并行计算}：大幅提升训练和推理效率
\item 🎯 \textbf{长距离建模}：有效捕捉长序列中的依赖关系
\item 📊 \textbf{可解释性}：注意力权重提供模型决策的可视化
\item 🔄 \textbf{架构简洁}：相对简单的结构，易于理解和实现
\item 📈 \textbf{性能卓越}：在多个任务上取得突破性进展
\end{itemize}

\textbf{对AI领域的深远影响：}
\begin{itemize}
\item 🏗️ \textbf{架构基础}：成为现代大模型的标准架构
\item 🌍 \textbf{跨领域应用}：从NLP扩展到CV、语音等领域
\item 📈 \textbf{性能突破}：推动多个AI任务达到新高度
\item 🔬 \textbf{研究方向}：引导AI研究的新方向
\end{itemize}

\textbf{在NLP领域的革命：}
\begin{itemize}
\item 📚 \textbf{预训练范式}：推动预训练+微调的发展
\item 🎯 \textbf{多任务学习}：一个模型处理多种NLP任务
\item 🌐 \textbf{多语言模型}：支持多语言的统一模型
\item 💡 \textbf{少样本学习}：强大的少样本和零样本能力
\end{itemize}

\textbf{对传媒行业的意义：}
\begin{itemize}
\item 📰 \textbf{内容生成}：高质量的自动内容生成
\item 🔍 \textbf{信息处理}：强大的文本理解和分析能力
\item 🌐 \textbf{多语言支持}：跨语言的内容处理
\item 🎯 \textbf{个性化服务}：基于深度理解的个性化推荐
\end{itemize}

\textbf{未来发展方向：}
\begin{itemize}
\item 📊 \textbf{效率优化}：减少计算复杂度，提高效率
\item 🎯 \textbf{架构创新}：探索新的注意力机制和架构
\item 🌐 \textbf{多模态融合}：结合文本、图像、音频等模态
\item 🔄 \textbf{持续学习}：支持在线学习和适应
\end{itemize}
\end{frame}

\begin{frame}{第11页：注意力可视化案例}
\frametitle{看见AI的"思考"：注意力可视化}

\textbf{注意力可视化的价值：}
\begin{itemize}
\item 👁️ \textbf{透明性}：让AI的决策过程可见
\item 🔍 \textbf{调试工具}：帮助发现模型的问题
\item 📚 \textbf{教学辅助}：帮助理解模型工作原理
\item 🎯 \textbf{应用指导}：指导模型的实际应用
\end{itemize}

\textbf{可视化示例1：句子内部关系}
\begin{lstlisting}
输入句子："The cat sat on the mat"
可视化显示：
- "cat" 强烈关注 "sat"（主谓关系）
- "sat" 关注 "on"（动词介词关系）
- "on" 关注 "mat"（介词宾语关系）
\end{lstlisting}

\textbf{可视化示例2：长距离依赖}
\begin{lstlisting}
输入句子："The book that I bought yesterday is very interesting"
可视化显示：
- "book" 与 "interesting" 有强连接
- "I" 与 "bought" 有强连接
- 跨越中间词汇的长距离关系
\end{lstlisting}

\textbf{可视化示例3：多头注意力分工}
\begin{lstlisting}
不同注意力头的专门化：
- Head 1：关注语法关系（主谓宾）
- Head 2：关注语义相关性
- Head 3：关注位置邻近性
- Head 4：关注长距离依赖
\end{lstlisting}

\textbf{在传媒应用中的价值：}
\begin{itemize}
\item 📰 \textbf{内容分析}：理解AI如何理解新闻内容
\item 🎯 \textbf{质量控制}：检查AI是否关注了正确的信息
\item 📊 \textbf{效果评估}：评估AI处理的准确性
\item 🔧 \textbf{模型优化}：根据注意力模式优化模型
\end{itemize}

\textbf{实际应用建议：}
\begin{itemize}
\item 🔍 \textbf{定期检查}：定期查看AI的注意力模式
\item 📊 \textbf{异常检测}：发现异常的注意力分布
\item 🎯 \textbf{任务优化}：根据注意力模式调整任务设计
\item 📚 \textbf{用户教育}：帮助用户理解AI的工作方式
\end{itemize}
\end{frame}

\begin{frame}{第12页：从Transformer到大语言模型}
\frametitle{架构演进：从Transformer到LLM}

\textbf{发展历程：}
\begin{lstlisting}
2017年 Transformer → 2018年 BERT/GPT-1 → 2019年 GPT-2 →
2020年 GPT-3 → 2022年 ChatGPT → 2023年 GPT-4
\end{lstlisting}

\textbf{关键演进节点：}
\begin{itemize}
\item 🏗️ \textbf{2017年 Transformer}：奠定架构基础
\item 🎯 \textbf{2018年 BERT}：双向编码器的突破
\item 📝 \textbf{2018年 GPT-1}：生成式预训练的开端
\item 🚀 \textbf{2019年 GPT-2}：规模扩大的质变
\item 🌍 \textbf{2020年 GPT-3}：涌现能力的显现
\item 💬 \textbf{2022年 ChatGPT}：对话能力的突破
\item 🎨 \textbf{2023年 GPT-4}：多模态的融合
\end{itemize}

\textbf{规模演进：}
\begin{itemize}
\item 📊 \textbf{参数量增长}：从百万级到千亿级
\item 📚 \textbf{数据规模}：从GB级到TB级训练数据
\item ⚡ \textbf{计算需求}：从单GPU到大规模集群
\item 🎯 \textbf{能力提升}：从单任务到通用智能
\end{itemize}

\textbf{架构优化：}
\begin{itemize}
\item 🔧 \textbf{效率改进}：减少计算复杂度
\item 🎯 \textbf{性能提升}：提高模型表现
\item 📊 \textbf{稳定性}：改善训练稳定性
\item 🌐 \textbf{可扩展性}：支持更大规模的模型
\end{itemize}

\textbf{能力演进：}
\begin{itemize}
\item 📚 \textbf{理解能力}：从简单分类到深度理解
\item ✍️ \textbf{生成能力}：从模板填充到创意写作
\item 🧮 \textbf{推理能力}：从简单匹配到复杂推理
\item 🎯 \textbf{泛化能力}：从特定任务到通用能力
\end{itemize}

\textbf{对传媒的影响：}
\begin{itemize}
\item 📰 \textbf{内容创作}：从辅助工具到创作伙伴
\item 🔍 \textbf{信息处理}：从简单分类到深度分析
\item 💬 \textbf{用户交互}：从关键词搜索到自然对话
\item 🎯 \textbf{个性化}：从粗糙推荐到精准理解
\end{itemize}
\end{frame}

% 第3部分：Tokenization
\section{Tokenization}

\begin{frame}{第13页：什么是Token？}
\frametitle{Token：AI理解语言的基本单位}

\textbf{Token的定义：}
\begin{itemize}
\item 🧩 \textbf{基本单位}：AI模型处理文本的最小单位
\item 🔤 \textbf{数字表示}：将文本转换为数字序列
\item 📊 \textbf{统一格式}：不同语言和符号的统一表示
\item 🎯 \textbf{模型输入}：神经网络的实际输入格式
\end{itemize}

\textbf{为什么需要Tokenization？}
\begin{itemize}
\item 🤖 \textbf{机器理解}：计算机只能处理数字，不能直接处理文字
\item 📊 \textbf{统一处理}：将不同类型的文本统一为数字序列
\item 🎯 \textbf{效率考虑}：合适的分词能提高处理效率
\item 🧠 \textbf{语义保持}：尽可能保持原文的语义信息
\end{itemize}

\textbf{Token的类型：}
\begin{itemize}
\item 🔤 \textbf{字符级}：每个字符是一个Token
\item 📝 \textbf{词级}：每个单词是一个Token
\item 🧩 \textbf{子词级}：介于字符和单词之间
\item 🎯 \textbf{句子级}：整个句子作为一个Token
\end{itemize}

\textbf{Token化的影响：}
\begin{itemize}
\item 🎯 \textbf{词汇表大小}：影响模型的参数量
\item 📊 \textbf{序列长度}：影响模型的计算复杂度
\item 🧠 \textbf{语义理解}：影响模型对语言的理解
\item ⚡ \textbf{处理效率}：影响训练和推理速度
\end{itemize}

\textbf{实际例子：}
\begin{lstlisting}
原文："Hello, world!"
字符级：["H", "e", "l", "l", "o", ",", " ", "w", "o", "r", "l", "d", "!"]
词级：["Hello", ",", "world", "!"]
子词级：["Hello", ",", "world", "!"] 或 ["Hel", "lo", ",", "wor", "ld", "!"]
\end{lstlisting}
\end{frame}

\begin{frame}{第14页：分词方法对比}
\frametitle{分词策略：各有优劣的选择}

\textbf{1. 字符级分词（Character-level）}

\textbf{优点：}
\begin{itemize}
\item 词汇表小，参数少
\item 没有未知词问题
\item 适合处理拼写错误
\item 支持任意语言
\end{itemize}

\textbf{缺点：}
\begin{itemize}
\item 序列长度很长
\item 难以捕捉词级语义
\item 计算效率低
\item 训练困难
\end{itemize}

\textbf{2. 词级分词（Word-level）}

\textbf{优点：}
\begin{itemize}
\item 保持词的完整语义
\item 序列长度适中
\item 符合人类理解习惯
\item 处理效率高
\end{itemize}

\textbf{缺点：}
\begin{itemize}
\item 词汇表巨大
\item 存在未知词问题
\item 难处理形态变化
\item 不同语言差异大
\end{itemize}

\textbf{3. 子词级分词（Subword-level）}

\textbf{优点：}
\begin{itemize}
\item 平衡词汇表大小和语义
\item 处理未知词能力强
\item 适应多种语言
\item 现代模型的主流选择
\end{itemize}

\textbf{缺点：}
\begin{itemize}
\item 分词结果不够直观
\item 需要额外的分词算法
\item 可能破坏词的完整性
\end{itemize}
\end{frame}

\begin{frame}{第5页：Self-Attention机制详解}
\frametitle{Self-Attention：序列内部的关系建模}

\textbf{Self-Attention的核心思想：}
\begin{itemize}
\item 🔍 \textbf{自我关注}：序列中每个位置关注其他所有位置
\item 🌐 \textbf{全局连接}：直接建模任意两个位置间的关系
\item 📊 \textbf{权重计算}：动态计算每个位置的重要性权重
\item 🎯 \textbf{信息整合}：基于权重整合全局信息
\end{itemize}

\textbf{三个关键概念：Query、Key、Value}
\begin{itemize}
\item 🔍 \textbf{Query（查询）}：当前位置想要查找的信息
\item 🗝️ \textbf{Key（键）}：每个位置提供的索引信息
\item 💎 \textbf{Value（值）}：每个位置包含的实际内容
\end{itemize}

\textbf{计算过程：}
\begin{lstlisting}
1. 计算相似度：Attention Score = Query × Key^T
2. 归一化权重：Attention Weight = Softmax(Score)
3. 加权求和：Output = Attention Weight × Value
\end{lstlisting}

\textbf{数学公式：}
$$\text{Attention}(Q,K,V) = \text{Softmax}\left(\frac{QK^T}{\sqrt{d_k}}\right)V$$

\textbf{直观理解：}
\begin{itemize}
\item 🎯 \textbf{相似度计算}：Query和Key的点积表示相关性
\item 📊 \textbf{权重归一化}：Softmax确保权重和为1
\item 🔄 \textbf{信息聚合}：根据权重组合Value信息
\item 📏 \textbf{缩放因子}：$\sqrt{d_k}$防止梯度过小
\end{itemize}

\textbf{优势特点：}
\begin{itemize}
\item ⚡ \textbf{并行计算}：所有位置可以同时计算
\item 🎯 \textbf{长距离建模}：直接连接远距离位置
\item 🔄 \textbf{动态权重}：根据输入内容动态调整
\item 📊 \textbf{可解释性}：注意力权重可视化
\end{itemize}
\end{frame}

\begin{frame}{第14页：分词方法对比}
\frametitle{分词策略：各有优劣的选择}

\textbf{1. 字符级分词（Character-level）}

\textbf{优点：}
\begin{itemize}
\item 词汇表小，参数少
\item 没有未知词问题
\item 适合处理拼写错误
\item 支持任意语言
\end{itemize}

\textbf{缺点：}
\begin{itemize}
\item 序列长度很长
\item 难以捕捉词级语义
\item 计算效率低
\item 训练困难
\end{itemize}

\textbf{2. 词级分词（Word-level）}

\textbf{优点：}
\begin{itemize}
\item 保持词的完整语义
\item 序列长度适中
\item 符合人类理解习惯
\item 处理效率高
\end{itemize}

\textbf{缺点：}
\begin{itemize}
\item 词汇表巨大
\item 存在未知词问题
\item 难处理形态变化
\item 不同语言差异大
\end{itemize}

\textbf{3. 子词级分词（Subword-level）}

\textbf{优点：}
\begin{itemize}
\item 平衡词汇表大小和语义
\item 处理未知词能力强
\item 适应多种语言
\item 现代模型的主流选择
\end{itemize}

\textbf{缺点：}
\begin{itemize}
\item 分词结果不够直观
\item 需要额外的分词算法
\item 可能破坏词的完整性
\end{itemize}

\textbf{主流子词分词算法：}
\begin{itemize}
\item 🔧 \textbf{BPE（Byte Pair Encoding）}：基于频率的合并算法
\item 🎯 \textbf{WordPiece}：Google开发，BERT使用
\item 📊 \textbf{SentencePiece}：支持多语言的统一分词
\item 🚀 \textbf{Unigram}：基于概率的分词方法
\end{itemize}

\textbf{选择考虑因素：}
\begin{itemize}
\item 🎯 \textbf{任务类型}：不同任务适合不同分词策略
\item 🌍 \textbf{语言特点}：考虑目标语言的特征
\item 📊 \textbf{数据规模}：数据量影响词汇表设计
\item ⚡ \textbf{计算资源}：平衡性能和效率
\end{itemize}
\end{frame}

\begin{frame}{第15页：BPE算法详解}
\frametitle{BPE：现代LLM的主流分词算法}

\textbf{BPE算法原理：}
\begin{itemize}
\item 🔤 \textbf{初始状态}：从字符级开始
\item 📊 \textbf{统计频率}：统计相邻字符对的出现频率
\item 🔄 \textbf{迭代合并}：反复合并最频繁的字符对
\item 🎯 \textbf{构建词汇表}：逐步构建子词词汇表
\end{itemize}

\textbf{算法步骤：}
\begin{enumerate}
\item \textbf{初始化}：将所有文本分解为字符
\item \textbf{统计}：计算所有相邻字符对的频率
\item \textbf{合并}：合并频率最高的字符对
\item \textbf{更新}：更新文本和频率统计
\item \textbf{重复}：重复步骤2-4直到达到目标词汇表大小
\end{enumerate}

\textbf{BPE示例：}
\begin{lstlisting}
初始文本：["low", "lower", "newest", "widest"]
字符级：["l o w", "l o w e r", "n e w e s t", "w i d e s t"]

迭代1：合并最频繁的"e s" → "es"
结果：["l o w", "l o w e r", "n e w es t", "w i d es t"]

迭代2：合并最频繁的"es t" → "est"
结果：["l o w", "l o w e r", "n e w est", "w i d est"]

继续迭代...
\end{lstlisting}

\textbf{BPE的优势：}
\begin{itemize}
\item 📊 \textbf{数据驱动}：基于实际数据的频率统计
\item 🎯 \textbf{平衡性}：平衡词汇表大小和语义保持
\item 🔄 \textbf{适应性}：能够适应不同的语言和领域
\item ⚡ \textbf{效率}：算法简单，计算效率高
\end{itemize}

\textbf{在LLM中的应用：}
\begin{itemize}
\item 🤖 \textbf{GPT系列}：使用BPE进行分词
\item 📊 \textbf{词汇表大小}：通常在30K-50K之间
\item 🎯 \textbf{多语言支持}：支持多种语言的统一处理
\item 🔄 \textbf{动态调整}：可以根据需要调整词汇表大小
\end{itemize}

\textbf{实际影响：}
\begin{itemize}
\item 📝 \textbf{文本表示}：影响模型对文本的内部表示
\item 🧠 \textbf{语义理解}：影响模型的语义理解能力
\item ⚡ \textbf{处理效率}：影响训练和推理的效率
\item 🎯 \textbf{模型性能}：直接影响模型的最终性能
\end{itemize}
\end{frame}

\begin{frame}{第16页：Token对模型理解的影响}
\frametitle{分词的艺术：如何影响AI的理解}

\textbf{Token粒度对理解的影响：}
\begin{itemize}
\item 🔍 \textbf{细粒度（字符级）}：
  \begin{itemize}
  \item 能够处理任意文本
  \item 但难以理解词汇语义
  \item 需要更多计算来组合语义
  \end{itemize}

\item 🎯 \textbf{中粒度（子词级）}：
  \begin{itemize}
  \item 平衡语义和灵活性
  \item 现代LLM的主流选择
  \item 能够处理大多数情况
  \end{itemize}

\item 📊 \textbf{粗粒度（词级）}：
  \begin{itemize}
  \item 保持完整词汇语义
  \item 但词汇表过大
  \item 存在未知词问题
  \end{itemize}
\end{itemize}

\textbf{具体影响案例：}
\begin{itemize}
\item 📝 \textbf{专业术语}：
  \begin{itemize}
  \item 好的分词：["machine", "learning"]
  \item 差的分词：["mach", "ine", "learn", "ing"]
  \item 影响：语义理解的准确性
  \end{itemize}

\item 🌍 \textbf{多语言文本}：
  \begin{itemize}
  \item 中文：需要考虑词汇边界
  \item 英文：相对容易分词
  \item 影响：跨语言理解能力
  \end{itemize}

\item 🔢 \textbf{数字和符号}：
  \begin{itemize}
  \item 数字分词：影响数值理解
  \item 特殊符号：影响格式理解
  \item 影响：结构化信息处理
  \end{itemize}
\end{itemize}

\textbf{在传媒应用中的考虑：}
\begin{itemize}
\item 📰 \textbf{新闻文本}：需要处理专业术语和人名地名
\item 📱 \textbf{社交媒体}：需要处理网络用语和表情符号
\item 🌐 \textbf{多语言内容}：需要统一的多语言分词策略
\item 📊 \textbf{结构化数据}：需要保持数据格式的完整性
\end{itemize}

\textbf{优化策略：}
\begin{itemize}
\item 🎯 \textbf{领域适配}：针对特定领域优化分词
\item 📊 \textbf{动态调整}：根据任务需求调整分词策略
\item 🔄 \textbf{持续优化}：基于使用效果持续改进
\item 🌍 \textbf{多语言考虑}：设计支持多语言的分词方案
\end{itemize}

\textbf{实际应用建议：}
\begin{itemize}
\item 🔍 \textbf{分析需求}：根据应用场景选择合适的分词策略
\item 📊 \textbf{测试效果}：通过实验验证分词效果
\item 🎯 \textbf{监控质量}：持续监控分词对模型性能的影响
\item 🔄 \textbf{迭代改进}：根据反馈不断优化分词策略
\end{itemize}
\end{frame}

% 第4部分：LLM训练过程
\section{LLM训练过程}

\begin{frame}{第17页：LLM训练概览}
\frametitle{LLM训练：从数据到智能的转化}

\textbf{训练阶段概览：}
\begin{lstlisting}
原始数据 → 数据预处理 → 预训练 → 微调 → RLHF → 部署应用
\end{lstlisting}

\textbf{三个主要阶段：}
\begin{enumerate}
\item \textbf{预训练（Pre-training）}
   \begin{itemize}
   \item 🎯 \textbf{目标}：学习语言的基本规律和知识
   \item 📚 \textbf{数据}：大规模无标注文本数据
   \item 🔄 \textbf{方法}：自监督学习，预测下一个词
   \item ⏱️ \textbf{时间}：数周到数月
   \end{itemize}

\item \textbf{微调（Fine-tuning）}
   \begin{itemize}
   \item 🎯 \textbf{目标}：适应特定任务或领域
   \item 📊 \textbf{数据}：少量高质量标注数据
   \item 🔧 \textbf{方法}：有监督学习，任务特定优化
   \item ⏱️ \textbf{时间}：数小时到数天
   \end{itemize}

\item \textbf{人类反馈强化学习（RLHF）}
   \begin{itemize}
   \item 🎯 \textbf{目标}：对齐人类价值观和偏好
   \item 👥 \textbf{数据}：人类标注的偏好数据
   \item 🎮 \textbf{方法}：强化学习，奖励模型指导
   \item ⏱️ \textbf{时间}：数天到数周
   \end{itemize}
\end{enumerate}

\textbf{训练资源需求：}
\begin{itemize}
\item 💻 \textbf{计算资源}：大规模GPU集群
\item 📊 \textbf{数据资源}：TB级高质量文本数据
\item ⏰ \textbf{时间资源}：数月的训练时间
\item 💰 \textbf{经济成本}：数百万到数千万美元
\end{itemize}

\textbf{训练挑战：}
\begin{itemize}
\item 📈 \textbf{规模挑战}：如何高效训练大规模模型
\item 📊 \textbf{数据质量}：如何确保训练数据的质量
\item 🎯 \textbf{目标对齐}：如何让模型符合人类期望
\item ⚖️ \textbf{伦理考虑}：如何避免有害内容的学习
\end{itemize}
\end{frame}
