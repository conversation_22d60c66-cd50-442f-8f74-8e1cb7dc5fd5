# 第2周详细备课材料：LLM工作原理与技术基础

## 教学目标深度解析

### 知识目标（Knowledge Objectives）
- **架构理解**：深入理解Transformer架构的设计原理和创新点
- **技术掌握**：掌握Tokenization、Self-Attention、位置编码等核心概念
- **训练认知**：理解LLM的预训练、微调、RLHF等训练过程
- **边界认知**：准确认识LLM的能力边界和"幻觉"现象

### 技能目标（Skills Objectives）
- **分析能力**：能够分析不同LLM的技术特点和适用场景
- **应用能力**：具备合理选择和使用LLM的能力
- **评估能力**：能够评估LLM输出的质量和可信度
- **优化能力**：初步具备优化LLM使用效果的能力

### 态度目标（Attitude Objectives）
- **科学态度**：理性看待LLM的技术原理和发展规律
- **批判思维**：对LLM输出保持批判性思维
- **学习热情**：激发对深度技术学习的兴趣
- **责任意识**：认识到使用LLM的责任和伦理要求

---

## 核心概念深度拓展

### 1. Transformer架构的革命性突破

#### 注意力机制的历史演进
**传统注意力机制（2015年）**
- **背景**：解决RNN在长序列处理中的信息损失问题
- **原理**：在编码器-解码器架构中引入注意力权重
- **应用**：机器翻译、图像描述生成
- **局限**：仍需要RNN作为基础架构

**自注意力机制的诞生（2017年）**
- **创新点**：完全抛弃RNN，纯粹基于注意力机制
- **核心思想**：让序列中的每个位置都能直接关注其他所有位置
- **技术优势**：并行计算、长距离依赖建模
- **实现方式**：通过Q、K、V矩阵运算实现

#### Self-Attention数学原理深化
**基础公式详解**
```
Attention(Q,K,V) = Softmax(QK^T/√d_k)V
```

**详细计算步骤**
1. **线性变换**：
   - Query: Q = XW_Q
   - Key: K = XW_K  
   - Value: V = XW_V

2. **相似度计算**：
   - Score = QK^T
   - 每个位置与所有位置的相似度

3. **缩放处理**：
   - Scaled Score = QK^T/√d_k
   - 防止softmax梯度过小

4. **权重归一化**：
   - Attention Weight = Softmax(Scaled Score)
   - 确保权重和为1

5. **加权求和**：
   - Output = Attention Weight × V
   - 获得最终的表示

**多头注意力的优势**
- **多角度关注**：不同的头关注不同类型的关系
- **表示丰富性**：增加模型的表达能力
- **并行计算**：多个头可以并行处理
- **专业化分工**：不同头学会专注不同模式

#### 位置编码的深层理解
**为什么需要位置编码**
- Transformer本质上是置换不变的（permutation invariant）
- 词序对语言理解至关重要
- 必须显式地注入位置信息

**正弦位置编码的设计哲学**
```
PE(pos, 2i) = sin(pos/10000^(2i/d_model))
PE(pos, 2i+1) = cos(pos/10000^(2i/d_model))
```

**设计考虑因素**
1. **唯一性**：每个位置有独特的编码
2. **相对性**：能够表示位置间的相对关系
3. **外推性**：能够处理训练时未见过的长度
4. **数值稳定性**：避免数值计算问题

**其他位置编码方法**
- **可学习位置编码**：通过训练学习位置表示
- **相对位置编码**：直接建模相对位置关系
- **旋转位置编码（RoPE）**：通过旋转操作编码位置
- **ALiBi**：通过线性偏置引入位置信息

### 2. Tokenization技术深度解析

#### BPE算法的详细实现
**算法伪代码**
```python
def bpe_algorithm(corpus, vocab_size):
    # 初始化：字符级别的词汇表
    vocab = set(char for word in corpus for char in word)
    
    # 初始化单词表示
    word_splits = {word: list(word) for word in corpus}
    
    while len(vocab) < vocab_size:
        # 统计相邻符号对的频率
        pairs = count_pairs(word_splits)
        
        # 选择频率最高的符号对
        best_pair = max(pairs, key=pairs.get)
        
        # 合并最频繁的符号对
        word_splits = merge_pair(word_splits, best_pair)
        
        # 添加到词汇表
        vocab.add(''.join(best_pair))
    
    return vocab, word_splits
```

**BPE的优势分析**
1. **数据驱动**：基于实际语料的频率统计
2. **语言无关**：适用于各种语言
3. **平衡性**：在词汇表大小和语义保持间平衡
4. **处理未知词**：能够处理训练时未见过的词

**BPE的局限性**
1. **贪心策略**：局部最优可能不是全局最优
2. **频率偏向**：高频词可能被过度细分
3. **语义破坏**：可能破坏有意义的词汇边界
4. **领域适应**：不同领域需要不同的分词策略

#### 其他分词算法对比

**WordPiece算法**
- **核心思想**：最大化训练数据的似然概率
- **选择标准**：选择使语言模型概率最大的合并
- **应用案例**：BERT模型使用
- **优势**：更好的语言模型性能

**SentencePiece算法**
- **核心思想**：统一的多语言分词框架
- **技术特点**：将空格也作为特殊字符处理
- **应用场景**：多语言模型训练
- **优势**：完全数据驱动，语言无关

**Unigram语言模型**
- **核心思想**：基于单词的概率分布
- **算法流程**：从大词汇表逐步删除低概率词
- **选择标准**：最小化语言模型的困惑度
- **应用案例**：T5模型使用

### 3. LLM训练过程深度剖析

#### 预训练阶段的技术细节

**数据预处理流程**
1. **原始数据收集**
   - Common Crawl网页数据
   - 书籍语料库
   - 学术论文数据库
   - 高质量网站内容

2. **数据清洗过程**
   - 去重处理（近似和精确去重）
   - 质量过滤（基于启发式规则）
   - 有害内容过滤
   - 语言检测和分离

3. **数据格式化**
   - 统一编码格式（UTF-8）
   - 文档边界标记
   - 特殊字符处理
   - 长度截断和填充

**损失函数设计**
```
Loss = -Σ log P(w_t | w_{<t})
```
- **自回归目标**：预测下一个词
- **交叉熵损失**：衡量预测分布与真实分布的差异
- **掩码处理**：排除填充位置的损失计算
- **梯度累积**：处理内存限制问题

**训练技巧和优化**
1. **学习率调度**
   - Warmup阶段：避免初期训练不稳定
   - 余弦退火：平滑的学习率衰减
   - 自适应调整：基于验证集性能调整

2. **正则化技术**
   - Dropout：防止过拟合
   - Layer Normalization：稳定训练过程
   - Weight Decay：参数正则化

3. **分布式训练**
   - 数据并行：跨GPU分布数据
   - 模型并行：跨GPU分布模型
   - 梯度同步：确保训练一致性

#### 微调技术的演进

**全参数微调（Full Fine-tuning）**
- **操作方式**：调整模型的所有参数
- **适用场景**：有充足数据和计算资源
- **优势**：通常能获得最佳性能
- **劣势**：计算成本高，容易过拟合

**参数高效微调（PEFT）技术**

**LoRA（Low-Rank Adaptation）详解**
- **核心思想**：在原权重矩阵旁添加低秩分解矩阵
- **数学原理**：W = W_0 + AB，其中A和B是低秩矩阵
- **参数量**：只需训练AB，大大减少参数量
- **性能**：在多数任务上接近全参数微调

**AdaLoRA（Adaptive LoRA）**
- **改进点**：动态调整不同层的秩
- **自适应机制**：根据重要性分配不同的秩
- **性能提升**：在相同参数量下性能更好

**Prefix Tuning**
- **核心思想**：只训练输入序列的前缀
- **实现方式**：在输入前添加可学习的prefix tokens
- **优势**：参数量极少，训练速度快
- **适用场景**：生成任务效果较好

**P-Tuning v2**
- **改进**：在每一层都添加可学习的prompt
- **深度prompt**：不仅在输入层，各隐藏层都有prompt
- **性能**：在理解类任务上表现更好

#### RLHF技术深度分析

**人类反馈的收集机制**
1. **标注员培训**
   - 制定详细的标注指南
   - 进行一致性测试
   - 定期质量检查
   - 处理标注分歧

2. **比较数据生成**
   - 对于同一输入，生成多个回答
   - 标注员对回答进行排序
   - 收集偏好数据
   - 质量控制和验证

3. **偏好建模**
   - 使用Bradley-Terry模型
   - 训练奖励模型
   - 处理不一致的标注
   - 考虑标注员差异

**奖励模型训练**
- **架构选择**：通常基于预训练语言模型
- **训练目标**：学习人类偏好模式
- **数据处理**：配对比较数据的处理
- **评估指标**：准确率、AUC等

**PPO算法的应用**
- **策略梯度**：直接优化策略参数
- **价值函数**：估计状态的长期价值
- **重要性采样**：处理策略更新的稳定性
- **KL散度约束**：防止策略偏离过远

### 4. 大模型的涌现能力分析

#### 涌现能力的定义和特征
**定义**：在模型规模达到某个临界点后突然出现的新能力

**典型涌现能力**
1. **Few-shot学习**：通过少量示例学习新任务
2. **复杂推理**：多步骤的逻辑推理能力
3. **代码生成**：从自然语言生成代码
4. **多语言理解**：跨语言的知识迁移

**涌现的数学特征**
- **非线性**：不能从小模型性能预测
- **突发性**：在某个规模点突然出现
- **不可分解**：不能简单地分解为子能力的组合

#### 规模定律（Scaling Laws）
**Chinchilla规模定律**
```
L(N,D) = A + B/N^α + C/D^β
```
其中：
- L：损失函数值
- N：模型参数量
- D：训练数据量
- A、B、C、α、β：拟合参数

**关键发现**
1. **计算最优**：给定计算预算下的最优模型大小和数据量配比
2. **数据重要性**：数据量的重要性被长期低估
3. **训练效率**：更小但训练更久的模型可能更优

---

## 教学案例库

### 案例1：GPT系列模型的演进分析

#### GPT-1的历史意义（2018年）
**技术背景**
- 基于Transformer解码器架构
- 首次展示预训练+微调范式的有效性
- 参数量：1.17亿

**训练细节**
- 数据：BookCorpus（约4GB文本）
- 训练时间：在8个GPU上训练1个月
- 优化器：Adam优化器
- 学习率：2.5e-4

**性能表现**
- 在多个NLP任务上达到当时最优
- 证明了无监督预训练的有效性
- 为后续大模型发展奠定基础

#### GPT-2的争议发布（2019年）
**技术突破**
- 参数量：15亿（比GPT-1增长13倍）
- 数据：WebText（40GB高质量网页文本）
- 架构改进：更深的网络，更多的注意力头

**争议事件**
- OpenAI最初拒绝发布完整模型
- 理由：担心被恶意使用生成虚假信息
- 社会反应：引发AI安全和开放性的讨论
- 最终结果：分阶段发布，促进负责任的AI发展

**能力展示**
- 流畅的文本生成
- 多样的写作风格
- 一定的事实记忆能力
- 初步的few-shot学习能力

#### GPT-3的范式转变（2020年）
**规模飞跃**
- 参数量：1750亿（比GPT-2增长116倍）
- 数据：Common Crawl、WebText2、Books1、Books2、Wikipedia
- 训练成本：估计460万美元

**涌现能力**
- **Few-shot learning**：通过几个示例就能学会新任务
- **In-context learning**：在上下文中学习，无需参数更新
- **代码生成**：能够根据描述生成代码
- **数学推理**：能够解决基本的数学问题

**商业影响**
- OpenAI API的推出
- 催生了大量AI应用
- 引发了大模型竞赛
- 改变了AI产业格局

#### GPT-4的多模态突破（2023年）
**技术特点**
- 多模态输入：支持文本和图像
- 更强的推理能力
- 更好的对齐效果
- 更高的安全性

**性能表现**
- 在多项基准测试中接近人类水平
- 通过各种专业考试
- 显著降低了有害内容生成

**应用影响**
- ChatGPT Plus的升级
- 大量第三方应用的涌现
- 推动了多模态AI的发展

### 案例2：BERT vs GPT：两条不同的发展路线

#### BERT的双向编码创新
**架构特点**
- 基于Transformer编码器
- 双向上下文理解
- 掩码语言模型训练目标
- 句子关系预测任务

**训练目标**
1. **掩码语言模型（MLM）**：预测被掩码的词
2. **下一句预测（NSP）**：判断两句话是否连续

**应用优势**
- 文本理解任务表现优异
- 适合分类、问答、命名实体识别
- 可以获得更好的语义表示

#### GPT的自回归生成优势
**架构特点**
- 基于Transformer解码器
- 单向（从左到右）处理
- 自回归语言建模
- 天然适合文本生成

**训练目标**
- 预测下一个词：P(w_t | w_{<t})
- 最大化序列概率

**应用优势**
- 文本生成能力强
- 适合对话、创作、翻译
- 可以进行开放域生成

#### 发展趋势对比
- **BERT系列**：RoBERTa、DeBERTa、ELECTRA
- **GPT系列**：GPT-2、GPT-3、GPT-4
- **统一趋势**：T5、GLM等统一架构的出现

### 案例3：中文大模型的发展历程

#### 技术挑战
1. **中文分词问题**：中文没有天然的词边界
2. **多语言混合**：中英文混合的处理
3. **文化特异性**：中文特有的语言现象
4. **数据质量**：高质量中文语料的获取

#### 代表性模型

**清华GLM系列**
- 技术特点：通用语言模型架构
- 训练策略：自回归空白填充
- 优势：统一的理解和生成能力

**百度文心大模型**
- 技术特点：知识增强的预训练
- 数据优势：搜索引擎积累的高质量数据
- 应用场景：搜索、推荐、对话

**阿里通义千问**
- 技术特点：多模态能力
- 产业集成：与阿里云深度集成
- 商业应用：电商、金融、政务

**字节跳动豆包**
- 技术特点：对话优化
- 应用集成：与抖音、今日头条集成
- 用户体验：注重用户交互体验

---

## 互动教学设计

### 开场互动：注意力机制可视化演示（15分钟）

#### 活动设计
1. **工具使用**：BertViz注意力可视化工具
2. **句子选择**：
   - "The cat sat on the mat"
   - "苹果公司发布了新产品"
   - "人工智能改变了新闻行业"

3. **观察要点**：
   - 不同注意力头关注的模式
   - 长距离依赖关系的建立
   - 语法和语义关系的捕捉

#### 教学目的
- 直观理解注意力机制的工作原理
- 观察多头注意力的分工合作
- 体会Transformer的强大之处

### 中段互动：Tokenization实验（20分钟）

#### 实验设计
1. **在线工具**：OpenAI Tokenizer
2. **测试文本**：
   ```
   "Hello, world!"
   "你好，世界！"
   "人工智能"
   "artificial intelligence"
   "ChatGPT is amazing"
   ```

3. **观察对比**：
   - 不同语言的分词差异
   - 同一概念在不同语言中的token数量
   - 特殊字符的处理方式

#### 讨论问题
1. 为什么中文字符通常需要更多token？
2. 这对中文LLM的使用有什么影响？
3. 如何优化中文的tokenization？

### 结尾互动：LLM能力边界测试（25分钟）

#### 测试设计
分成4组，每组测试不同类型的能力：

**第1组：事实性知识测试**
- 测试LLM对具体事实的掌握
- 观察"幻觉"现象的出现
- 记录准确率和错误类型

**第2组：逻辑推理测试**
- 设计简单的逻辑推理题
- 观察推理过程的合理性
- 测试复杂推理的局限性

**第3组：创意生成测试**
- 测试创意写作能力
- 评估创意的新颖性和合理性
- 探索创意生成的边界

**第4组：多语言能力测试**
- 测试跨语言理解能力
- 观察不同语言的性能差异
- 探讨多语言模型的优势

#### 总结讨论
- 各组分享测试结果
- 分析LLM的优势和局限
- 讨论如何合理使用LLM

---

## 作业设计与评估

### 作业1：LLM技术原理分析报告（个人作业）

#### 作业要求
选择一个具体的LLM（如GPT-3、BERT、T5等），深入分析其技术原理。

#### 报告结构
1. **模型概述**（300字）
   - 模型基本信息
   - 发布时间和背景
   - 主要创新点

2. **架构分析**（600字）
   - 网络结构详解
   - 关键技术组件
   - 与其他模型的区别

3. **训练过程**（400字）
   - 训练数据和方法
   - 损失函数设计
   - 优化策略

4. **能力评估**（400字）
   - 在各项任务上的表现
   - 优势和局限性分析
   - 适用场景

5. **未来展望**（200字）
   - 技术发展方向
   - 潜在改进点
   - 对行业的影响

#### 评估标准
| 评估维度 | 权重 | 详细标准 |
|---------|------|----------|
| 技术理解深度 | 30% | 对技术原理的理解是否深入准确 |
| 分析逻辑性 | 25% | 分析是否有逻辑性，论证是否充分 |
| 信息准确性 | 20% | 技术信息是否准确无误 |
| 表达清晰性 | 15% | 语言表达是否清晰易懂 |
| 批判性思维 | 10% | 是否有独立的批判性见解 |

### 作业2：LLM应用效果测试（团队作业）

#### 作业要求
3-4人一组，设计实验测试某个LLM在特定任务上的表现。

#### 可选任务类型
1. **文本摘要**：测试不同长度和类型文本的摘要质量
2. **情感分析**：测试对不同情感表达的识别准确性
3. **问答系统**：测试对不同类型问题的回答质量
4. **创意写作**：测试创意内容的质量和多样性
5. **代码生成**：测试从自然语言到代码的转换能力

#### 实验设计要求
1. **实验假设**：明确的研究假设
2. **测试数据**：设计或收集测试数据集
3. **评估指标**：定义量化的评估指标
4. **实验流程**：详细的实验执行步骤
5. **结果分析**：深入的结果分析和讨论

#### 实验报告结构
1. **研究背景**：实验的背景和意义
2. **实验设计**：详细的实验设计方案
3. **实验实施**：实验执行过程记录
4. **结果分析**：实验结果的统计分析
5. **讨论与结论**：对结果的讨论和结论
6. **改进建议**：对实验设计的改进建议

#### 评估标准
- 实验设计的科学性（30%）
- 实验执行的严谨性（25%）
- 结果分析的深度（20%）
- 团队协作表现（15%）
- 报告质量（10%）

---

## 教学资源清单

### 核心技术资源

#### 重要论文（必读）
1. **"Attention Is All You Need"** (2017)
   - 作者：Vaswani et al.
   - 意义：Transformer架构的奠基论文
   - 关键点：自注意力机制、位置编码、多头注意力

2. **"BERT: Pre-training Deep Bidirectional Transformers"** (2018)
   - 作者：Devlin et al.
   - 意义：双向编码器的突破
   - 关键点：掩码语言模型、双向上下文

3. **"GPT-3: Language Models are Few-Shot Learners"** (2020)
   - 作者：Brown et al.
   - 意义：展示了大规模模型的涌现能力
   - 关键点：few-shot learning、in-context learning

#### 技术博客和教程
1. **The Illustrated Transformer**
   - 网址：http://jalammar.github.io/illustrated-transformer/
   - 特点：图解Transformer架构
   - 适合：初学者理解基本概念

2. **The Illustrated BERT**
   - 网址：http://jalammar.github.io/illustrated-bert/
   - 特点：可视化BERT工作原理
   - 适合：理解双向编码机制

3. **Hugging Face Course**
   - 网址：https://huggingface.co/course/
   - 特点：实践导向的NLP教程
   - 适合：动手实践和应用开发

### 实验和可视化工具

#### 在线工具
1. **OpenAI Tokenizer**
   - 网址：https://platform.openai.com/tokenizer
   - 功能：测试不同文本的tokenization结果

2. **BertViz**
   - 网址：https://github.com/jessevig/bertviz
   - 功能：可视化注意力机制
   - 安装：pip install bertviz

3. **Transformer Explainer**
   - 网址：https://poloclub.github.io/transformer-explainer/
   - 功能：交互式Transformer结构解释

#### 开发框架
1. **Hugging Face Transformers**
   - 安装：pip install transformers
   - 特点：丰富的预训练模型库
   - 文档：https://huggingface.co/transformers/

2. **PyTorch**
   - 安装：pip install torch
   - 特点：深度学习框架
   - 文档：https://pytorch.org/docs/

3. **TensorFlow**
   - 安装：pip install tensorflow
   - 特点：Google开发的深度学习框架
   - 文档：https://www.tensorflow.org/

### 数据集资源

#### 预训练数据集
1. **Common Crawl**
   - 描述：大规模网页文本数据
   - 网址：https://commoncrawl.org/
   - 用途：大规模预训练

2. **OpenWebText**
   - 描述：高质量网页文本
   - 网址：https://github.com/jcpeterson/openwebtext
   - 用途：GPT-2复现训练

3. **The Pile**
   - 描述：多样化的文本数据集
   - 网址：https://pile.eleuther.ai/
   - 用途：大规模语言模型训练

#### 评估基准
1. **GLUE**
   - 描述：通用语言理解评估基准
   - 网址：https://gluebenchmark.com/
   - 任务：分类、回归、相似度等

2. **SuperGLUE**
   - 描述：更具挑战性的评估基准
   - 网址：https://super.gluebenchmark.com/
   - 任务：阅读理解、常识推理等

3. **MMLU**
   - 描述：大规模多任务语言理解
   - 网址：https://github.com/hendrycks/test
   - 任务：57个不同学科的选择题

### 学习资源

#### 在线课程
1. **CS224N: NLP with Deep Learning (Stanford)**
   - 网址：http://web.stanford.edu/class/cs224n/
   - 特点：理论与实践并重
   - 适合：系统学习NLP

2. **Deep Learning for NLP (Oxford)**
   - 网址：https://www.cs.ox.ac.uk/teaching/courses/2016-2017/dl/
   - 特点：数学理论深入
   - 适合：理论基础学习

#### 书籍推荐
1. **《自然语言处理综论》**
   - 作者：Daniel Jurafsky, James H. Martin
   - 特点：NLP领域经典教材
   - 适合：全面了解NLP

2. **《深度学习》**
   - 作者：Ian Goodfellow等
   - 特点：深度学习理论基础
   - 适合：数学基础学习

3. **《注意力机制》**
   - 作者：Andrea Galassi等
   - 特点：专门讲解注意力机制
   - 适合：深入理解注意力

---

## 课堂管理建议

### 技术准备
1. **网络要求**：稳定的互联网连接（用于在线演示）
2. **设备要求**：投影设备、音响设备
3. **软件准备**：浏览器、Python环境（可选）
4. **备用方案**：离线演示材料

### 难度控制
1. **分层教学**：
   - 基础概念（所有学生）
   - 技术细节（有兴趣的学生）
   - 数学推导（选修内容）

2. **渐进式深入**：
   - 从直观理解开始
   - 逐步引入技术细节
   - 最后讨论前沿发展

### 互动设计
1. **提问技巧**：
   - 开放式问题引发思考
   - 引导式问题帮助理解
   - 对比式问题加深印象

2. **讨论组织**：
   - 小组讨论促进参与
   - 全班讨论分享观点
   - 个人思考时间充足

### 时间控制
- **理论讲解**：45分钟
- **互动演示**：25分钟
- **讨论总结**：20分钟

---

## 常见问题解答

### Q1: Transformer为什么能够并行计算？
**A**: 关键在于自注意力机制的设计：
- **不依赖序列顺序**：每个位置都能同时计算
- **矩阵运算**：可以用矩阵乘法并行处理
- **无递归结构**：不像RNN需要逐步计算
- **位置编码**：通过位置编码保持序列信息

### Q2: 为什么需要这么大的模型参数？
**A**: 大参数量的必要性：
- **知识存储**：需要大量参数存储世界知识
- **模式学习**：复杂的语言模式需要更多参数
- **泛化能力**：更多参数带来更好的泛化
- **涌现能力**：某些能力只在大规模时出现

### Q3: 如何理解LLM的"幻觉"现象？
**A**: "幻觉"的产生机制：
- **概率生成**：基于概率选择下一个词
- **训练目标**：训练时只是预测下一个词，不强调真实性
- **知识混淆**：可能混合不同来源的信息
- **缺乏验证**：无法实时验证生成内容的准确性

### Q4: 中文LLM面临哪些特殊挑战？
**A**: 主要挑战包括：
- **分词问题**：中文没有明确的词边界
- **字符编码**：中文字符数量庞大
- **语言特性**：汉语的语法和表达特点
- **数据质量**：高质量中文语料相对较少
- **多语言混合**：中英文混合使用的处理

### Q5: 如何评估LLM的能力？
**A**: 多维度评估体系：
- **任务性能**：在具体任务上的表现
- **鲁棒性**：面对干扰输入的稳定性
- **一致性**：相同输入产生一致输出的能力
- **安全性**：避免生成有害内容的能力
- **效率**：计算资源的使用效率

---

*本备课材料为第2周课程的详细补充，包含了深度的技术原理、丰富的案例分析和实践活动设计。建议根据学生的技术背景和兴趣调整深度和重点。*