# AI驱动的传媒内容制作 - 课程作业模板

## 📝 作业提交格式要求

### 基本信息
```
课程名称：AI驱动的传媒内容制作
学生姓名：[姓名]
学号：[学号]
班级：[班级]
作业周次：第X周
提交日期：[日期]
```

### 文件命名规范
- 格式：`第X周_姓名_学号_作业类型.文件格式`
- 示例：`第1周_张三_2021001_调研报告.docx`

## 📋 各周作业模板

### 第1周：AI认知与调研

#### 作业1：传媒机构AI应用调研报告
**要求**：选择一个传媒机构，调研其AI技术应用现状，写一份500字的调研报告

**模板**：
```markdown
# [机构名称]AI技术应用调研报告

## 1. 机构基本信息
- 机构名称：
- 机构类型：
- 规模：
- 主要业务：

## 2. AI技术应用现状
### 2.1 应用领域
- [ ] 内容生产
- [ ] 内容分发
- [ ] 用户服务
- [ ] 数据分析
- [ ] 其他：

### 2.2 具体应用案例
1. 应用场景1：
   - 技术方案：
   - 实施效果：
   
2. 应用场景2：
   - 技术方案：
   - 实施效果：

## 3. 应用效果分析
### 3.1 积极影响
- 效率提升：
- 成本节约：
- 质量改善：

### 3.2 面临挑战
- 技术挑战：
- 人员挑战：
- 管理挑战：

## 4. 个人思考
- 对该机构AI应用的评价：
- 可改进之处：
- 对传媒行业的启示：

## 5. 参考资料
1. 
2. 
3. 
```

#### 作业2：AI工具体验报告
**要求**：注册并体验至少2个AI工具，记录使用感受

**模板**：
```markdown
# AI工具体验报告

## 工具1：[工具名称]
### 基本信息
- 开发公司：
- 主要功能：
- 使用方式：
- 费用：

### 体验过程
- 注册过程：
- 界面设计：
- 功能测试：

### 使用感受
- 优点：
- 缺点：
- 适用场景：
- 评分（1-10）：

## 工具2：[工具名称]
[同上格式]

## 对比分析
| 维度 | 工具1 | 工具2 |
|------|-------|-------|
| 易用性 | | |
| 功能丰富度 | | |
| 回答质量 | | |
| 响应速度 | | |
| 中文支持 | | |

## 总结与建议
- 个人偏好：
- 使用建议：
- 学习收获：
```

### 第3周：提示词设计练习

#### 作业：提示词设计与优化
**要求**：针对指定场景设计提示词，并进行效果对比

**模板**：
```markdown
# 提示词设计练习

## 场景1：新闻稿写作
### 任务描述
为某公司新产品发布写一篇新闻稿

### 提示词版本1（初版）
```
请帮我写一篇新闻稿，关于我们公司的新产品。
```

### AI回答1
[粘贴AI回答]

### 提示词版本2（改进版）
```
你是一位资深的企业新闻发言人，请为以下新产品发布写一篇专业的新闻稿：

产品信息：
- 产品名称：[具体名称]
- 产品类型：[类型]
- 主要功能：[功能列表]
- 目标用户：[用户群体]
- 上市时间：[时间]

要求：
- 字数：300-400字
- 语调：正式、专业
- 结构：标题+导语+正文+结语
- 突出产品创新点和市场价值
```

### AI回答2
[粘贴AI回答]

### 效果对比分析
| 评估维度 | 版本1 | 版本2 | 改进效果 |
|----------|-------|-------|----------|
| 内容完整性 | | | |
| 专业程度 | | | |
| 结构清晰度 | | | |
| 信息准确性 | | | |

### 学习总结
- 改进要点：
- 设计心得：
- 应用建议：
```

### 第5周：信息验证练习

#### 作业：新闻事件信息搜集与验证
**要求**：选择近期新闻事件，使用AI搜集信息并进行验证

**模板**：
```markdown
# 新闻事件信息搜集与验证报告

## 1. 事件基本信息
- 事件名称：
- 发生时间：
- 涉及地点：
- 主要人物：
- 事件性质：

## 2. AI信息搜集过程
### 2.1 使用的AI平台
- 平台1：[名称]
- 平台2：[名称]
- 平台3：[名称]

### 2.2 搜集的信息
#### 基础事实信息
- 时间线：
- 关键人物：
- 重要数据：

#### 不同观点
- 观点1：
  - 来源：
  - 主要论据：
  
- 观点2：
  - 来源：
  - 主要论据：

### 2.3 发现的问题
- 信息不一致之处：
- 可疑信息：
- 缺失信息：

## 3. 信息验证过程
### 3.1 验证方法
- [ ] 官方渠道确认
- [ ] 权威媒体对比
- [ ] 多源交叉验证
- [ ] 专家观点查证
- [ ] 其他：

### 3.2 验证结果
| 信息内容 | 验证方法 | 验证结果 | 可信度 |
|----------|----------|----------|--------|
| | | | |
| | | | |

## 4. 最终结论
- 事件真实情况：
- AI信息的准确性评估：
- 验证过程中的发现：

## 5. 学习反思
- 信息验证的重要性：
- AI使用的注意事项：
- 改进建议：
```

### 第9周：创意策划练习

#### 作业：主题创意生成
**要求**：为"环保"主题生成20个不同角度的内容创意

**模板**：
```markdown
# "环保"主题创意生成报告

## 1. 创意生成过程
### 1.1 使用的AI工具
- 主要工具：
- 辅助工具：

### 1.2 提示词策略
```
[粘贴使用的提示词]
```

## 2. 创意清单
| 序号 | 创意标题 | 内容角度 | 目标受众 | 传播形式 |
|------|----------|----------|----------|----------|
| 1 | | | | |
| 2 | | | | |
| ... | | | | |
| 20 | | | | |

## 3. 创意分类分析
### 3.1 按内容角度分类
- 科普教育类：[数量]个
- 生活实用类：[数量]个
- 政策解读类：[数量]个
- 案例故事类：[数量]个
- 其他：[数量]个

### 3.2 按传播形式分类
- 图文内容：[数量]个
- 视频内容：[数量]个
- 音频内容：[数量]个
- 互动内容：[数量]个

## 4. 优秀创意详细策划
### 创意1：[标题]
- 核心概念：
- 内容大纲：
- 制作要求：
- 预期效果：

### 创意2：[标题]
[同上格式]

## 5. 创意评估
### 5.1 评估标准
- 新颖性：
- 可行性：
- 传播力：
- 教育价值：

### 5.2 自我评估
- 最满意的创意：
- 最具挑战的创意：
- 改进空间：

## 6. 学习总结
- AI在创意生成中的作用：
- 创意思维的启发：
- 实际应用建议：
```

### 第15周：期末项目提案

#### 作业：期末项目提案书
**要求**：制定详细的期末项目执行计划

**模板**：
```markdown
# 期末项目提案书

## 1. 项目基本信息
- 项目名称：
- 项目类型：
  - [ ] 播客节目策划制作
  - [ ] 社交媒体账号运营
  - [ ] 深度报道完成
  - [ ] 数据新闻制作
  - [ ] 其他：
- 预计完成时间：
- 团队成员：（如适用）

## 2. 项目背景与目标
### 2.1 项目背景
- 选题原因：
- 目标受众：
- 市场需求：

### 2.2 项目目标
- 主要目标：
- 具体指标：
- 预期成果：

## 3. AI技能应用规划
### 3.1 计划使用的AI技能
- [ ] 信息搜集与验证
- [ ] 内容摘要与提炼
- [ ] 创意生成与策划
- [ ] 文本写作与润色
- [ ] 数据分析与处理
- [ ] 其他：

### 3.2 具体应用场景
| AI技能 | 应用环节 | 预期效果 | 使用工具 |
|--------|----------|----------|----------|
| | | | |
| | | | |

## 4. 项目执行计划
### 4.1 项目阶段划分
- 第一阶段（时间）：
  - 主要任务：
  - 交付成果：
  
- 第二阶段（时间）：
  - 主要任务：
  - 交付成果：
  
- 第三阶段（时间）：
  - 主要任务：
  - 交付成果：

### 4.2 时间安排
| 时间节点 | 任务内容 | 负责人 | 完成标准 |
|----------|----------|--------|----------|
| | | | |
| | | | |

## 5. 资源需求与风险评估
### 5.1 资源需求
- 技术资源：
- 信息资源：
- 人力资源：
- 其他资源：

### 5.2 风险评估
- 技术风险：
- 时间风险：
- 质量风险：
- 应对措施：

## 6. 成果展示计划
### 6.1 展示形式
- 主要形式：
- 辅助材料：
- 展示时长：

### 6.2 评估标准
- 技术应用（30%）：
- 内容质量（40%）：
- 创新性（20%）：
- 完整性（10%）：

## 7. 学习期望
- 希望通过项目掌握的技能：
- 预期遇到的挑战：
- 解决方案设想：
```

## 📊 作业评分标准

### 基础作业评分（100分制）
- **完成度**（30分）：按要求完成所有内容
- **质量**（40分）：内容准确、分析深入、表达清晰
- **创新性**（20分）：有独特见解或创新应用
- **规范性**（10分）：格式规范、提交及时

### 项目作业评分（100分制）
- **技术应用**（30分）：AI技能运用的广度和深度
- **内容质量**（40分）：内容价值和表达质量
- **创新性**（20分）：创新思维和独特性
- **完整性**（10分）：项目完成度和规范性

## 📅 作业提交时间表

| 周次 | 作业内容 | 提交截止时间 | 提交方式 |
|------|----------|--------------|----------|
| 第1周 | 调研报告+工具体验 | 第2周周一 23:59 | 在线提交 |
| 第3周 | 提示词设计练习 | 第4周周一 23:59 | 在线提交 |
| 第5周 | 信息验证练习 | 第6周周一 23:59 | 在线提交 |
| ... | ... | ... | ... |
| 第15周 | 期末项目提案 | 第15周周五 23:59 | 在线提交 |
| 第16周 | 期末项目成果 | 第16周课堂展示 | 现场展示 |

---

*请严格按照模板格式完成作业，确保内容完整、格式规范*
