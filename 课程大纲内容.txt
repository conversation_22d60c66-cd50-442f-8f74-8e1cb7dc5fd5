汕头大学本科教学
课程教学大纲
汕头大学长江新闻与传播学院
2025年4月
1、课程简介（Course Description）
本课程以 “发挥信息化驱动引领作用” 为核心理念，对人工智能大模型于传媒领域的创新应用展开系统性探究。​
课程聚焦传媒行业内容生产的全流程，借助丰富的实战演练、案例剖析以及工具探索，助力学生熟练掌握运用大模型开展高效信息采集、深度内容摘要、多元创意策划等核心技能，并灵活运用提示词和文档等进阶交互技巧。在学习过程中，结合中华优秀传统文化传播、主流价值观弘扬等案例，培育学生运用人工智能技术服务文化传承与社会发展的能力，增强其文化自信与行业使命感。同时，课程着重强调传媒工作者的社会责任，引导学生在实践中秉持职业操守，确保技术应用契合社会主流价值导向。​
本课程运用 ASK 教学法，向学生阐释大模型的基础原理（K），助其理解大模型在传媒领域的运行机制，熟练运用大模型完成基础传媒任务（S）。课程融入中华优秀传统文化数字化传播、主流媒体舆论引导等项目，推动学生深入领会传媒行业的使命（A）。​
课程的高阶性体现于系统性、前瞻性的培养导向，创新性体现于教学案例紧密贴合行业前沿，挑战度体现于要求学生独立完成AI+数据分析项目。通过本课程的学习，学生能够迅速掌握 AI 大模型的应用方法，将其融入传媒工作，提升工作效率与内容质量，成长为兼具专业能力与思想深度的新时代传媒人才。​
本课程适用于网络与新媒体专业学生修读。
2、预期学习结果（Intended Learning Outcomes）
本课程的预期学习结果如下表：
3、先修要求（Pre-requisite）
无
4、教材及其他教学资源（Textbooks and Other Learning Resources）
指定教材：
无
推荐教材：
《自然语言处理：基于预训练模型的方法》（车万翔等著），电子工业出版社
课程网站：
中国大学MOOC在线开放课程：《人工智能与创新》
https://www.icourse163.org/course/NKU-1471736170?from=searchPage&outVendor=zw_mooc_pcssjg_
工具与平台实践：
国内主流大模型平台：腾讯元宝、豆包、文心一言、通义千问、Kimi、智谱清言。
5、主要教学环节（Teaching and Learning Activities）
6、课程考核（Assessment Scheme）
7、学习进度（Course Schedule）
课程名（COURSE TITLE）： | AI驱动的传媒内容制作 | AI驱动的传媒内容制作
课程代码（COURSE CODE）： | JOU2155A | JOU2155A
学分（CREDIT VALUE）： | 2 | 2
课内课时（CONTACT HOURS）： | 32 | 32
先修课要求（PRE-REQUISITE） | 无 | 无
开课单位（DEPARTMENT/UNIT）： | 长江新闻与传播学院 | 长江新闻与传播学院
版本（VERSION）：
课程负责人（COURSE COORDINATOR）： | 马琪昌 (签章)
审核人（APPROVER）： | (签章)
审核日期（APPROVE DATE）：
知识单元 | 知识点 | 初始程度 | 要求程度 | 预期学习结果
1. AI 与 LLM 基础 | 1.1 AI 发展简史与核心思想 | L1 | L2 | 理解 AI 发展关键里程碑，掌握机器学习、深度学习核心思想
1. AI 与 LLM 基础 | 1.2 大语言模型（LLM）概述 | L1 | L2 | 理解 LLM 的诞生、突破，以及在传媒各环节的应用潜力
1. AI 与 LLM 基础 | 1.3 LLM 工作原理 | L1 | L2 | 了解 Transformer 架构、Tokenization 概念，LLM 训练过程及能力边界
2. 提示词工程基础 | 2.1 提示词核心地位与构成要素 | L1 | L3 | 掌握优质提示词的 CRISPE 框架，熟练运用基础提示模式
2. 提示词工程基础 | 2.2 精确指令与格式控制 | L1 | L3 | 能明确任务指令，提供上下文，指定输出格式、长度与风格
3. 核心应用场景 | 3.1 智能信息获取 | L1 | L3 | 利用 LLM 进行基础搜索、深度研究，掌握事实核查与交叉验证
3. 核心应用场景 | 3.2 文本摘要与提炼 | L1 | L3 | 完成基础及高级文本摘要，提取关键信息与结构化数据
3. 核心应用场景 | 3.3 创意生成 | L1 | L3 | 借助 LLM 进行选题策划、辅助写作与风格转换
4. 进阶技巧与工具 | 4.1 高级提示技巧 | L1 | L3 | 运用 Few-Shot、角色扮演、思维链等技巧解决复杂问题
4. 进阶技巧与工具 | 4.2 主流 LLM 平台与辅助工具 | L1 | L3 | 熟悉国内外主流 LLM 平台及 AI 辅助工具，能按需选择使用
5. 综合应用 | 5.1 内容生产全流程整合 | L1 | L3 | 将所学技能整合应用于传媒内容生产流程与一些相关的新传类比赛，如数据新闻
教学环节 | 理论课（小时） | 习题课（小时） | 实验（小时） | 研讨（小时） | 社会实践（小时） | 项目（小时） | 在线学习（小时） | 期中测试（小时） | 合计
课内 | 16 | 16 | 32
课外 | 32 | 40 | 72
考核项目 | 考核内容和考试方法简介 | 权重
日常考勤 | 课堂随机点名 | 10%
课堂考查 | 课堂检查实操情况及课堂提问 | 10%
日常作业 | 根据预期学习结果要点布置作业 | 30%
期末项目 | 借助AI完成一个内容生产相关的综合性项目（如策划并制作一期播客节目、运营一个模拟社交媒体账号一周、完成一篇小型深度报道、完成一个数据新闻等等），择优挑选参加各类相关的比赛（可适当加分）。 | 50%
周次 | 教学时数 | 教学形式 | 教学内容
1 | 2 | 课堂教学 | 课程导论；AI 发展简史（关键里程碑）；机器学习、深度学习核心思想；大语言模型（LLM）的诞生与突破；LLM 在传媒各环节（采、编、播、发、评）的应用潜力概览；讨论：AI 是助手、伙伴还是颠覆者？
2 | 2 | 课堂教学 | Transformer 架构简介（注意力机制的直观理解）；Tokenization（分词）概念；LLM 的训练过程（预训练、微调）；LLM 的能力边界与局限性（为何会 “一本正经胡说八道”？）；体验不同 LLM 对同一简单问题的回答差异
3 | 2 | 课堂教学 | 提示词的核心地位；优质提示词的构成要素（CRISPE 框架：Capacity and Role, Insight, Statement, Personality, Experiment）；基础提示模式详解（指令、问答、补全、对话）；避免常见错误（模糊、歧义、隐含假设）；提示词诊所（分析不良提示词并改进）
4 | 2 | 课堂教学 | 明确任务指令的重要性；提供充分的上下文信息；指定输出格式（列表、要点、Markdown、JSON 基础）；控制输出长度与风格（正式、口语、幽默等）；练习使用提示词控制 AI 输出的格式和风格
5 | 2 | 课堂教学 | 利用 LLM 进行事实查询、概念解释、背景信息搜集；设计有效的查询型提示词；识别与初步应对 “幻觉”；交叉验证方法（结合传统搜索、权威来源）；针对特定新闻事件，使用 LLM 搜集多方观点，并进行事实核查练习
6 | 2 | 课堂教学 | 利用 LLM 进行复杂问题探究、文献综述辅助、多角度信息整合；研究型提示词进阶（限定范围、追问细节）；评估 LLM 分析性回答的可靠性；选择一个研究课题，尝试使用 LLM 辅助完成初步的文献回顾或背景分析
7 | 2 | 课堂教学 | 摘要类型（抽取式 vs. 生成式）；LLM 生成摘要的原理；基础摘要提示词（指定长度、要点）；应用场景（新闻快讯、会议纪要、报告阅读）；对不同长度和类型的文本（新闻、评论、访谈）进行摘要练习
8 | 2 | 课堂教学 | 生成特定受众 / 风格的摘要；关键信息提取（实体、观点、数据、情感倾向）；结构化信息提取（如提取表格数据）；从用户评论中提取产品优缺点和情感倾向；将非结构化文本转换为结构化数据
9 | 2 | 课堂教学 | 利用 LLM 拓宽思路：生成大量点子、不同角度、反向思考；辅助选题策划：热点追踪、受众兴趣分析（基于 LLM 理解）、内容日历建议；针对特定主题（如 “大学生就业”），利用 LLM 生成 10 个不同的新闻报道角度或内容栏目创意
10 | 2 | 课堂教学 | 辅助搭建内容框架（大纲、脚本）；生成初稿段落、提供多种表达；文本润色与改写（扩写、缩写、简化、提升专业度）；模仿特定写作风格；将一篇平实的报道改写成更具吸引力的社交媒体文案；模仿某位知名作家的风格写一段文字
11 | 2 | 课堂教学 | 深入理解 Zero-Shot vs. Few-Shot；如何设计有效的 Few-Shot 示例；角色扮演（Persona）的最佳实践（细节设定、一致性）；应用：快速定制特定任务、生成高度风格化内容；设计 Few-Shot 提示，让 LLM 学会一种特定的评论打分模式；让 LLM 扮演不同角色（如用户、专家、反对者）进行模拟对话
12 | 2 | 课堂教学 | CoT 原理再探；不同 CoT 提示方法（Zero-shot CoT, Manual CoT）；CoT 的适用场景（逻辑推理、数学问题、规划分析）；Self-Consistency（多路径推理取优）；使用 CoT 解决一个需要多步骤推理的传媒分析问题（如评估某公关策略的潜在风险）
13 | 2 | 课堂教学 | 深入体验与对比：ChatGPT/GPT-4, Claude, Gemini, 文心一言，通义千问，讯飞星火，智谱清言，Kimi Chat 等；各自的特点、优势、局限性；账号注册、基本操作、付费模式（如适用）；使用不同平台完成同一组任务，进行横向测评
14 | 2 | 课堂教学 | 了解集成 LLM 的软件（豆包、ima、腾讯元宝等）；特定领域 AI 工具（如Trae、Cursor）；API 概念简介（了解即可）；体验这些工具，探索其在资料整理、写作辅助方面的功能
15 | 2 | 课堂教学 | 选择一个贯穿内容生产相关的综合性项目（如策划并制作一期播客节目、运营一个模拟社交媒体账号一周、完成一篇小型深度报道等等）；要求综合运用本课程所学的各项 LLM 技能（信息搜集、内容生成、摘要提炼、提示词技巧等）；分组或个人完成项目；Q&A
16 | 2 | 课堂教学 | 期末项目成果展示与互评；课程知识体系回顾与梳理；LLM 技术发展趋势与对内容生产的持续影响；个人学习路径建议；Q&A