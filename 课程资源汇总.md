# AI驱动的传媒内容制作 - 课程资源汇总

## 📚 必读教材与参考书籍

### 核心教材
1. **《自然语言处理：基于预训练模型的方法》** - 车万翔等著，电子工业出版社
2. **《人工智能简史》** - 尼克·博斯特罗姆著
3. **《提示词工程完全指南》** - 在线资源

### 推荐参考书
1. **《智能时代》** - 吴军著
2. **《深度学习》** - Ian Goodfellow等著
3. **《与AI对话的艺术》** - 实用指南
4. **《数字时代的信息素养》** - 媒体素养教育

## 🌐 在线学习资源

### MOOC课程
- **中国大学MOOC**：《人工智能与创新》
  - 链接：https://www.icourse163.org/course/NKU-1471736170
- **Coursera**：AI for Everyone
- **edX**：Introduction to Artificial Intelligence

### 技术博客与网站
- **OpenAI Blog**：最新AI技术动态
- **Anthropic Research**：Claude相关研究
- **Google AI Blog**：谷歌AI技术分享
- **百度AI技术博客**：国内AI发展动态

## 🛠️ 推荐AI工具平台

### 国外主流平台
1. **ChatGPT/GPT-4** (OpenAI)
   - 功能：通用对话、文本生成、代码辅助
   - 特点：功能全面，生态丰富
   - 适用：各类文本任务

2. **Claude** (Anthropic)
   - 功能：长文本处理、安全对话
   - 特点：安全性高，逻辑清晰
   - 适用：复杂分析任务

3. **Gemini** (Google)
   - 功能：多模态处理
   - 特点：与Google服务集成
   - 适用：搜索相关任务

### 国内主流平台
1. **文心一言** (百度)
   - 功能：中文理解、知识问答
   - 特点：中文优化，本土化好
   - 适用：中文内容创作

2. **通义千问** (阿里)
   - 功能：商业应用、办公辅助
   - 特点：企业级应用
   - 适用：商业文档处理

3. **讯飞星火** (科大讯飞)
   - 功能：语音识别、多模态
   - 特点：语音技术强
   - 适用：音频内容处理

4. **智谱清言** (智谱AI)
   - 功能：代码生成、学术写作
   - 特点：技术导向
   - 适用：技术文档

5. **Kimi Chat** (月之暗面)
   - 功能：长文本处理
   - 特点：超长上下文
   - 适用：长文档分析

### 专业辅助工具
1. **豆包** - 办公场景集成
2. **腾讯元宝** - 多模态功能
3. **Cursor** - 代码辅助
4. **Notion AI** - 笔记整理

## 📖 每周阅读材料清单

### 第1-2周：AI基础
- 《人工智能简史》第1-2章
- 新华社《AI主播上岗记》案例
- MIT Technology Review: "AI in Journalism"

### 第3-4周：提示词工程
- 《提示词工程指南》
- OpenAI官方提示词最佳实践
- Anthropic Claude提示词指南

### 第5-6周：信息获取
- 《事实核查手册》
- 《AI可信度评估指南》
- 路透社事实核查方法论

### 第7-8周：文本处理
- 《自动文摘技术发展》
- 《新闻摘要写作规范》
- 《结构化数据处理指南》

### 第9-10周：创意生成
- 《新闻选题策划指南》
- 《创意思维与传媒实践》
- 《AI时代的写作技巧》

### 第11-12周：高级技巧
- 《高级提示工程技术》
- 《思维链推理技术详解》
- 《逻辑分析在传媒中的应用》

### 第13-14周：工具平台
- 《LLM平台评测报告2024》
- 《AI工具集成应用指南》
- 各平台官方技术文档

### 第15-16周：项目实践
- 《项目管理基础》
- 《AI技术发展趋势报告》
- 优秀学生项目案例

## 🎯 实践练习资源

### 提示词模板库
```
# 新闻写作模板
你是一位资深记者，请根据以下信息写一篇新闻报道：
- 事件：[具体事件]
- 时间：[发生时间]
- 地点：[发生地点]
- 人物：[相关人物]
- 要求：客观、准确、吸引人，字数约500字

# 摘要生成模板
请为以下文本生成摘要：
- 长度：[字数要求]
- 受众：[目标读者]
- 重点：[关注要点]
- 格式：[输出格式]

# 创意策划模板
请为[主题]生成创意方案：
- 目标受众：[受众描述]
- 传播渠道：[媒体平台]
- 创意要求：[具体要求]
- 输出：10个不同角度的创意点子
```

### 数据集与案例库
- 新闻文本数据集
- 社交媒体内容样本
- 用户评论数据
- 传媒行业案例集

## 🔧 技术工具指南

### 文本编辑工具
- **Markdown编辑器**：Typora, Mark Text
- **在线协作**：腾讯文档, 飞书文档
- **格式转换**：Pandoc

### 数据分析工具
- **Excel/WPS表格**：基础数据处理
- **Python**：高级数据分析
- **在线工具**：图表生成器

### 项目管理工具
- **任务管理**：Todoist, 滴答清单
- **时间规划**：番茄工作法应用
- **文件管理**：云盘同步工具

## 📊 评估工具与标准

### 技能评估清单
- [ ] AI基础概念理解
- [ ] 提示词设计能力
- [ ] 信息验证技巧
- [ ] 内容创作能力
- [ ] 工具选择判断
- [ ] 项目综合应用

### 项目评估标准
1. **技术应用**（30%）
   - AI技能运用的广度和深度
   - 工具选择的合理性
   - 技术问题解决能力

2. **内容质量**（40%）
   - 内容的原创性和价值
   - 信息的准确性和可靠性
   - 表达的清晰性和吸引力

3. **创新性**（20%）
   - 应用场景的创新
   - 解决方案的独特性
   - 思维方式的突破

4. **完整性**（10%）
   - 项目的完成度
   - 文档的规范性
   - 展示的专业性

## 🎓 学习建议与发展路径

### 短期目标（课程期间）
1. 掌握基础AI概念和LLM原理
2. 熟练使用主流AI平台
3. 具备独立完成AI辅助项目的能力

### 中期目标（毕业前）
1. 深入某个专业领域的AI应用
2. 建立个人的AI工具使用体系
3. 培养AI伦理和社会责任意识

### 长期目标（职业发展）
1. 成为AI时代的复合型传媒人才
2. 引领行业的AI应用创新
3. 推动传媒行业的智能化转型

## 📞 学习支持与交流

### 课程交流群
- 微信群：AI传媒学习交流
- QQ群：传媒AI技术讨论
- 钉钉群：课程作业提交

### 答疑时间
- 每周三 14:00-16:00 办公室答疑
- 每周五 19:00-20:00 在线答疑

### 学习伙伴制度
- 2-3人学习小组
- 定期交流学习心得
- 互相检查作业完成情况

---

*本资源汇总将持续更新，请关注最新版本*
